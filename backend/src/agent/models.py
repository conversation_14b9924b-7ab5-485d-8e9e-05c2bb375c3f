from enum import StrEnum
from uuid import UUID

from ninja_feedback_client.models import RatingFeedback, SuperNinjaAgentRunFeedback
from pydantic import BaseModel


class AgentRunFeedback(BaseModel):
    feedback_id: UUID
    data: SuperNinjaAgentRunFeedback


class AgentRunFeedbackUpsertRequest(BaseModel):
    data: SuperNinjaAgentRunFeedback


class AgentRunStatus(StrEnum):
    RUNNING = "running"
    STOPPED = "stopped"
    FAILED = "failed"
    COMPLETED = "completed"
    SUSPENDED = "suspended"
    ERROR = "error"
    ERROR_NOTIFICATION = "error-notification"
