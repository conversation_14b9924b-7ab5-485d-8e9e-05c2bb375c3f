import asyncio
import hashlib
import mimetypes
from os.path import dirname, join
from time import time
from typing import Optional

from agentpress.thread_manager import ThreadManager
from agentpress.tool import Too<PERSON><PERSON><PERSON><PERSON>, openapi_schema, xml_schema
from ninja_common.context import context
from ninja_common.models.context_models import NinjaContextKey
from pydantic import BaseModel
from sandbox.sandbox import SandboxToolsBase
from utils.clients import get_cloudfront_client, get_s3_client
from utils.files_utils import clean_path
from utils.logger import logger

PROJECT_HASH_LENGTH = 8


class SandboxDeployToolConfig(BaseModel):
    bucket_name: str
    distribution_id: str
    deployed_url: str


class DeploymentFileError(Exception):
    """Error when an issue occurs with a file on the sandbox"""

    pass


class DeploymentUploadError(Exception):
    """Error when deployment resources fail to upload to S3"""

    pass


class DeploymentInvalidationError(Exception):
    """Error when CloudFront cache invalidation fails"""

    pass


class DeploymentInvalidationTimeoutError(Exception):
    """Error when CloudFront cache invalidation times out"""

    pass


class SandboxDeployTool(SandboxToolsBase):
    """Tool for deploying static websites from a Daytona sandbox to Cloudflare Pages."""

    def __init__(
        self, project_id: str, thread_manager: ThreadManager, config: SandboxDeployToolConfig
    ):
        super().__init__(project_id, thread_manager)
        self.workspace_path = "/workspace"  # Ensure we're always operating in /workspace
        self.config = config

    def clean_path(self, path: str) -> str:
        """Clean and normalize a path to be relative to /workspace"""
        return clean_path(path, self.workspace_path)

    def _get_deployment_hash(self, name: str):
        project_hash = (
            hashlib.md5(f"{self.sandbox_id}-{name}".encode("utf-8"))
            .hexdigest()
            .lower()[:PROJECT_HASH_LENGTH]
        )
        return project_hash

    def _get_deployed_url(self, deployment_name: str):
        return f"https://{self.config.deployed_url}/{self._get_s3_deployment_path(deployment_name)}/index.html"

    def _get_s3_deployment_path(self, deployment_name: str):
        return join(context[NinjaContextKey.NINJA_USER_ID], deployment_name)

    async def _check_path_is_directory(self, path: str):
        try:
            file_info = await self.sandbox.fs.get_file_info(path)
            return file_info.is_dir
        except Exception as e:
            raise DeploymentFileError(f"Error occurred checking file info: {path}") from e

    async def _upload_project_files(self, deployment_name: str, base_path: str):
        # Define a common list to await all tasks generated by recursion
        upload_tasks = []
        deployment_path = self._get_s3_deployment_path(deployment_name)

        # Handle the download of the file and upload in a background thread
        async def upload_file(filename: str, path: str):
            logger.info(f"Downloading {filename} from sandbox...")
            content = await self.sandbox.fs.download_file(path)
            # When we upload to S3 we need the path in the bucket to be
            # relative to the base path of the workspace
            # We use 2 calls to remove prefix to enforce no / at the front
            relative_path = path.removeprefix(base_path).removeprefix("/")
            s3_key = join(deployment_path, relative_path)
            # Fallback to html if the type can't be guessed
            content_type = mimetypes.guess_type(filename)[0] or "text/html"
            logger.info(
                f"Uploading {filename} to s3 at location {dirname(relative_path)}",
                extra={"s3_path": s3_key, "content_type": content_type},
            )

            await asyncio.to_thread(
                get_s3_client().put_object,
                Bucket=self.config.bucket_name,
                Key=s3_key,
                Body=content,
                ContentType=content_type,
            )

        # Define a function to handle nested folders cleanly
        async def recursive_upload(path: str):
            files = await self.sandbox.fs.list_files(path)
            logger.info(
                f"Grabbed {len(files)} files from {path} to upload from sandbox",
                extra={"files": [file.name for file in files]},
            )
            for file in files:
                file_path = join(path, file.name)
                # Handle directories recursively
                if await self._check_path_is_directory(file_path):
                    await recursive_upload(file_path)
                    continue

                # Handle website assets
                upload_tasks.append(upload_file(filename=file.name, path=file_path))

        await recursive_upload(base_path)
        results = await asyncio.gather(*upload_tasks, return_exceptions=True)
        logger.info("Finished all website asset uploads")
        exceptions = [r for r in results if isinstance(r, Exception)]
        if exceptions:
            logger.error(
                f"Failed to upload website files to S3, encountered {len(exceptions)} errors"
            )
            raise DeploymentUploadError("Failed to upload website resources") from exceptions[0]

    async def _invalidate_project_cache(self, deployment_name: str, timeout: Optional[int] = 120):
        try:
            logger.info("Beginning CloudFront cache invalidation.")
            cloudfront = get_cloudfront_client()
            invalidation_id = (
                await asyncio.to_thread(
                    cloudfront.create_invalidation,
                    DistributionId=self.config.distribution_id,
                    InvalidationBatch={
                        "Paths": {
                            "Quantity": 1,
                            "Items": [f"/{self._get_s3_deployment_path(deployment_name)}/*"],
                        },
                        "CallerReference": str(time()),
                    },
                )
            )["Invalidation"]["Id"]

            async def poll_invalidation(invalidation_id: str):
                while True:
                    status = (
                        await asyncio.to_thread(
                            cloudfront.get_invalidation,
                            DistributionId=self.config.distribution_id,
                            Id=invalidation_id,
                        )
                    )["Invalidation"]["Status"]
                    if status.lower() == "completed":
                        return
                    await asyncio.sleep(1)

            await asyncio.wait_for(poll_invalidation(invalidation_id), timeout=timeout)
            logger.info("Invalidated deployment's CloudFront cache so website will refresh.")
        except asyncio.TimeoutError as e:
            logger.error(f"CloudFront invalidation did not complete within {timeout} seconds.")
            raise DeploymentInvalidationTimeoutError(
                "Refreshing website resources timed out"
            ) from e
        except Exception as e:
            logger.exception("CloudFront invalidation failed")
            raise DeploymentInvalidationError("Failed to refresh website resources") from e

    @openapi_schema(
        {
            "type": "function",
            "function": {
                "name": "deploy",
                "description": "Deploy a static website (HTML+CSS+JS) from a directory in the sandbox to Cloudflare Pages. Only use this tool when permanent deployment to a production environment is needed. The directory path must be relative to /workspace. The website will be deployed to {name}.pages.dev.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "name": {
                            "type": "string",
                            "description": "Name for the deployment, will be used in the URL as {name}.pages.dev",
                        },
                        "directory_path": {
                            "type": "string",
                            "description": "Path to the directory containing the static website files to deploy, relative to /workspace (e.g., 'build')",
                        },
                    },
                    "required": ["name", "directory_path"],
                },
            },
        }
    )
    @xml_schema(
        tag_name="deploy",
        mappings=[
            {"param_name": "name", "node_type": "attribute", "path": "name"},
            {
                "param_name": "directory_path",
                "node_type": "attribute",
                "path": "directory_path",
            },
        ],
        example="""
        <!--
        IMPORTANT: Only use this tool when:
        1. The user explicitly requests permanent deployment to production
        2. You have a complete, ready-to-deploy directory

        NOTE: If the same name is used, it will redeploy to the same project as before
                -->

        <deploy name="my-site" directory_path="website">
        </deploy>
        """,
    )
    async def deploy(self, name: str, directory_path: str) -> ToolResult:
        """
        Deploy a static website (HTML+CSS+JS) from the sandbox to Cloudflare Pages.
        Only use this tool when permanent deployment to a production environment is needed.

        Args:
            name: Name for the deployment, will be used in the URL as {name}.pages.dev
            directory_path: Path to the directory to deploy, relative to /workspace

        Returns:
            ToolResult containing:
            - Success: Deployment information including URL
            - Failure: Error message if deployment fails
        """
        try:
            # Ensure sandbox is initialized
            await self._ensure_sandbox()

            # Verify the directory exists
            directory_path = self.clean_path(directory_path)
            full_path = join(self.workspace_path, directory_path)
            if not await self._check_path_is_directory(full_path):
                raise DeploymentFileError(f"'{directory_path}' is not a directory")

            deployment_hash = self._get_deployment_hash(name)
            logger.info(
                f"Starting website deployment",
                extra={"project_path": full_path, "deployment_name": deployment_hash},
            )

            await self._upload_project_files(deployment_hash, full_path)
            await self._invalidate_project_cache(deployment_hash)

            deployed_url = self._get_deployed_url(deployment_hash)
            logger.info(
                f"Deployed website successfully",
                extra={"project_path": full_path, "url": deployed_url},
            )
            return self.success_response(
                {
                    "message": f"Website deployed successfully",
                    "website-url": deployed_url,
                }
            )

        except Exception as e:
            logger.exception(f"Error deploying website")
            return self.fail_response(f"Error deploying website: {str(e)}")


if __name__ == "__main__":
    import asyncio
    import sys

    async def test_deploy():
        # Replace these with actual values for testing
        sandbox_id = "sandbox-ccb30b35"
        password = "test-password"

        # Initialize the deploy tool
        deploy_tool = SandboxDeployTool(sandbox_id, password)

        # Test deployment - replace with actual directory path and site name
        result = await deploy_tool.deploy(
            name="test-site-1x",
            directory_path="website",  # Directory containing static site files
        )
        print(f"Deployment result: {result}")

    asyncio.run(test_deploy())
