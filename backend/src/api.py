import asyncio
import os
from collections import OrderedDict
from contextlib import asynccontextmanager
from datetime import datetime, timezone

# Import the agent API module
from agent import api as agent_api
from dotenv import load_dotenv
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from ninja_common.request_id import get_configured_middleware
from sandbox import api as sandbox_api, sandbox
from services import billing as billing_api
from services.supabase import DBConnection
from utils.config import EnvMode, config
from utils.logger import logger

# Load environment variables (these will be available through config)
load_dotenv()

# Initialize managers
instance_id = "single"

# Rate limiter state
ip_tracker = OrderedDict()
MAX_CONCURRENT_IPS = 25


@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    logger.info(
        f"Starting up FastAPI application with instance ID: {instance_id} in {config.ENV_MODE.value} mode"
    )

    try:
        sandbox.ensure_sandbox_image()

        db = DBConnection()
        # Initialize the agent API with shared resources
        agent_api.initialize(instance_id)

        # Initialize Redis connection
        from services import redis

        try:
            await redis.initialize_async()
            logger.info("Redis connection initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Redis connection: {e}")
            # Continue without Redis - the application will handle Redis failures gracefully

        # Start background tasks
        asyncio.create_task(agent_api.clean_up_suspended_agent_runs())

        yield

        # Clean up agent resources
        logger.info("Cleaning up agent resources")
        await agent_api.cleanup()

        # Clean up Redis connection
        try:
            logger.info("Closing Redis connection")
            await redis.close()
            logger.info("Redis connection closed successfully")
        except Exception as e:
            logger.error(f"Error closing Redis connection: {e}")

        # Clean up database connection
        logger.info("Disconnecting from database")
        await db.disconnect()
    except Exception as e:
        logger.error(f"Error during application startup: {e}")
        raise


app = FastAPI(lifespan=lifespan, middleware=get_configured_middleware())


# Define allowed origins based on environment
allowed_origins = [
    "http://localhost:3000",
    os.environ["SUPER_AGENT_FRONTEND_URL"],
]

# Add staging-specific origins
if config.ENV_MODE == EnvMode.STAGING:
    allowed_origins.append("http://localhost:3000")

# Add local-specific origins
if config.ENV_MODE == EnvMode.LOCAL:
    allowed_origins.append("http://localhost:3000")

app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["Content-Type", "Authorization"],
)

# Include the agent router with a prefix
app.include_router(agent_api.router, prefix="/api")

# Include the sandbox router with a prefix
app.include_router(sandbox_api.router, prefix="/api")

# Include the billing router with a prefix
app.include_router(billing_api.router, prefix="/api")


@app.get("/health")
@app.get("/api/health")
async def health_check():
    """Health check endpoint to verify API is working."""
    return {
        "status": "ok",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "instance_id": instance_id,
    }


if __name__ == "__main__":
    import uvicorn

    workers = 2

    logger.info(f"Starting server on 0.0.0.0:80 with {workers} workers")
    uvicorn.run("api:app", host="0.0.0.0", port=8000, workers=workers, reload=True)
