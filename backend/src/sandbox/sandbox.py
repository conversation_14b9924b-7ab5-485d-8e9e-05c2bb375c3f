import os
from functools import cache
from typing import Optional

from agentpress.thread_manager import ThreadManager
from agentpress.tool import Tool
from daytona_api_client import SandboxState
from daytona_sdk import (
    AsyncDaytona,
    AsyncSandbox,
    CreateSandboxFromSnapshotParams,
    CreateSnapshotParams,
    Daytona,
    DaytonaConfig,
    DaytonaError,
    Image,
    Resources,
    SessionExecuteRequest,
)
from utils.config import config
from utils.files_utils import clean_path
from utils.logger import logger

logger.debug("Initializing Daytona sandbox configuration")


@cache
def get_daytona_config() -> DaytonaConfig:
    return DaytonaConfig(
        api_key=config.DAYTONA_API_KEY,
        api_url=config.DAYTONA_SERVER_URL,
        target=config.DAYTONA_TARGET,
    )


if get_daytona_config().api_key:
    logger.debug("Daytona API key configured successfully")
else:
    logger.warning("No Daytona API key found in environment variables")

if get_daytona_config().api_url:
    logger.debug(f"Daytona server URL set to: {get_daytona_config().api_url}")
else:
    logger.warning("No Daytona server URL found in environment variables")

if get_daytona_config().target:
    logger.debug(f"Daytona target set to: {get_daytona_config().target}")
else:
    logger.warning("No Daytona target found in environment variables")


@cache
def get_daytona() -> AsyncDaytona:
    return AsyncDaytona(get_daytona_config())


def get_daytona_sync() -> Daytona:
    return Daytona(get_daytona_config())


logger.debug("Daytona client initialized")


async def get_or_start_sandbox(sandbox_id: str) -> AsyncSandbox:
    """Retrieve a sandbox by ID, check its state, and start it if needed."""

    logger.info(f"Getting or starting sandbox with ID: {sandbox_id}")

    try:
        sandbox = await get_daytona().get(sandbox_id)

        # Check if sandbox needs to be started
        if sandbox.state == SandboxState.ARCHIVED or sandbox.state == SandboxState.STOPPED:
            logger.info(f"Sandbox is in {sandbox.state} state. Starting...")
            try:
                await get_daytona().start(sandbox)
                # Wait a moment for the sandbox to initialize
                # sleep(5)
                # Refresh sandbox state after starting
                sandbox = await get_daytona().get(sandbox_id)

                # Start supervisord in a session when restarting
                await start_supervisord_session(sandbox)
            except Exception as e:
                logger.error(f"Error starting sandbox: {e}")
                raise e

        logger.info(f"Sandbox {sandbox_id} is ready")
        return sandbox

    except Exception as e:
        logger.error(f"Error retrieving or starting sandbox: {str(e)}")
        raise e


async def start_supervisord_session(sandbox: AsyncSandbox):
    """Start supervisord in a session."""
    session_id = "supervisord-session"
    try:
        logger.info(f"Creating session {session_id} for supervisord")
        # Make sure session_id doesn't contain characters that might cause path parsing issues
        sanitized_session_id = session_id.replace("/", "_").replace(":", "_")

        await sandbox.process.create_session(sanitized_session_id)

        # Execute supervisord command, task will end when session is closed
        await sandbox.process.execute_session_command(
            sanitized_session_id,
            SessionExecuteRequest(
                command="exec /usr/bin/supervisord -n -c /etc/supervisor/conf.d/supervisord.conf",
                run_async=True,
            ),
        )
        logger.info(f"Supervisord started in session {sanitized_session_id}")
    except Exception as e:
        logger.error(f"Error starting supervisord session: {str(e)}")
        raise e


async def create_sandbox(password: str, project_id: str | None = None) -> AsyncSandbox:
    """Create a new sandbox with all required services configured and running."""

    logger.debug("Creating new Daytona sandbox environment")
    logger.debug("Configuring sandbox with browser-use image and environment variables")

    labels = None
    if project_id:
        logger.debug(f"Using sandbox_id as label: {project_id}")
        labels = {"id": project_id}

    params = CreateSandboxFromSnapshotParams(
        snapshot=config.SANDBOX_IMAGE,
        public=True,
        labels=labels,
        env_vars={
            "CHROME_PERSISTENT_SESSION": "true",
            "RESOLUTION": "1024x768x24",
            "RESOLUTION_WIDTH": "1024",
            "RESOLUTION_HEIGHT": "768",
            "VNC_PASSWORD": password,
            "ANONYMIZED_TELEMETRY": "false",
            "CHROME_PATH": "",
            "CHROME_USER_DATA": "",
            "CHROME_DEBUGGING_PORT": "9222",
            "CHROME_DEBUGGING_HOST": "localhost",
            "CHROME_CDP": "",
        },
    )

    # Create the sandbox
    sandbox = await get_daytona().create(params=params)
    logger.debug(f"Sandbox created with ID: {sandbox.id}")

    # Start supervisord in a session for new sandbox
    await start_supervisord_session(sandbox)

    logger.debug(f"Sandbox environment successfully initialized")
    return sandbox


def ensure_sandbox_image():
    dir_path = os.path.dirname(os.path.realpath(__file__))
    image = Image.from_dockerfile(
        os.path.abspath(os.path.join(dir_path, "docker", "Dockerfile")),
    )

    try:
        logger.info(f"Creating sandbox image ({config.SANDBOX_IMAGE}) if not exists...")
        # This has to use the sync client as async client uses boto3 in async context and is broken.
        get_daytona_sync().snapshot.create(
            params=CreateSnapshotParams(
                name=config.SANDBOX_IMAGE,
                image=image,
                resources=Resources(
                    cpu=2,
                    memory=4,
                    disk=5,
                ),
            ),
            on_logs=logger.info,
        )
    except DaytonaError as e:
        # This is very hacky but Daytone raises this generic error without any specific message to identify
        # At the best this will err on the side of being too conservative and fail to start
        if "already exists" in str(e):
            logger.info(
                f"Sandbox image ({config.SANDBOX_IMAGE}) already exists, skipping creation.",
                extra={"error_message": str(e)},
            )
            return

        if "Forbidden resource" in str(e):
            logger.info(
                "You do not have permission to create/update sandbox images, continuing assuming no changes",
                extra={"error_message": str(e)},
            )
            return
        logger.error(f"Error creating sandbox image: {e}")
        raise e


class SandboxToolsBase(Tool):
    """Base class for all sandbox tools that provides project-based sandbox access."""

    # Class variable to track if sandbox URLs have been printed
    _urls_printed = False

    def __init__(self, project_id: str, thread_manager: Optional[ThreadManager] = None):
        super().__init__()
        self.project_id = project_id
        self.thread_manager = thread_manager
        self.workspace_path = "/workspace"
        self._sandbox = None
        self._sandbox_id = None
        self._sandbox_pass = None

    async def _ensure_sandbox(self) -> AsyncSandbox:
        """Ensure we have a valid sandbox instance, retrieving it from the project if needed."""
        if self._sandbox is None:
            try:
                # Get database client
                client = await self.thread_manager.db.client

                # Get project data
                project = (
                    await client.table("projects")
                    .select("*")
                    .eq("project_id", self.project_id)
                    .execute()
                )
                if not project.data or len(project.data) == 0:
                    raise ValueError(f"Project {self.project_id} not found")

                project_data = project.data[0]
                sandbox_info = project_data.get("sandbox", {})

                if not sandbox_info.get("id"):
                    raise ValueError(f"No sandbox found for project {self.project_id}")

                # Store sandbox info
                self._sandbox_id = sandbox_info["id"]
                self._sandbox_pass = sandbox_info.get("pass")

                # Get or start the sandbox
                self._sandbox = await get_or_start_sandbox(self._sandbox_id)

                # # Log URLs if not already printed
                # if not SandboxToolsBase._urls_printed:
                #     vnc_link = self._sandbox.get_preview_link(6080)
                #     website_link = self._sandbox.get_preview_link(8080)

                #     vnc_url = vnc_link.url if hasattr(vnc_link, 'url') else str(vnc_link)
                #     website_url = website_link.url if hasattr(website_link, 'url') else str(website_link)

                #     print("\033[95m***")
                #     print(f"VNC URL: {vnc_url}")
                #     print(f"Website URL: {website_url}")
                #     print("***\033[0m")
                #     SandboxToolsBase._urls_printed = True

            except Exception as e:
                logger.error(
                    f"Error retrieving sandbox for project {self.project_id}: {str(e)}",
                    exc_info=True,
                )
                raise e

        return self._sandbox

    @property
    def sandbox(self) -> AsyncSandbox:
        """Get the sandbox instance, ensuring it exists."""
        if self._sandbox is None:
            raise RuntimeError("Sandbox not initialized. Call _ensure_sandbox() first.")
        return self._sandbox

    @property
    def sandbox_id(self) -> str:
        """Get the sandbox ID, ensuring it exists."""
        if self._sandbox_id is None:
            raise RuntimeError("Sandbox ID not initialized. Call _ensure_sandbox() first.")
        return self._sandbox_id

    def clean_path(self, path: str) -> str:
        """Clean and normalize a path to be relative to /workspace."""
        cleaned_path = clean_path(path, self.workspace_path)
        logger.debug(f"Cleaned path: {path} -> {cleaned_path}")
        return cleaned_path
