'use client';

import styles from './SidebarLeft.module.scss';
import { useIsMobile } from '@/hooks/use-mobile';
import { useRef } from 'react';
import { OverlayBackground } from '@/components/ui/OverlayBackground';
import { useSidebar } from '@/components/ui/sidebar';
import { NavSidebar } from '@/components/sidebar/SidebarLeft/components/NavSidebar';
import { LeftPanelSection } from '@/components/sidebar/SidebarLeft/components/LeftPanelSection';

export const SidebarLeft = () => {
  const overlayRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const isMobile = useIsMobile();
  const { isLeftSidePanelOpen, setIsLeftSidePanelOpen } = useSidebar();

  const handleClose = () => {
    setIsLeftSidePanelOpen(false);
  };

  return (
    <>
      {isMobile && (
        <OverlayBackground
          showOverlay={isLeftSidePanelOpen}
          onClick={handleClose}
          zIndex={31}
          withTransparentTablet
          ref={overlayRef}
        />
      )}
      <div className={styles.root} ref={containerRef}>
        {!isMobile && <NavSidebar />}
        <LeftPanelSection />
      </div>
    </>
  );
};
