@use '@styles/index' as *;

.root {
  @extend %flex-row;
  gap: 0;
  height: 100%;
  box-sizing: border-box;
  background: var(--nj-background-surface-primary);
}

.container {
  @extend %flex-column;
  gap: 0;
  height: 100%;
  min-width: var(--nj-left-navigation-width-mobile);

  @media screen and (min-width: $nj-breakpoint-desktop) {
    min-width: var(--nj-left-navigation-width);
  }
}

.header {
  @extend %flex-between;
  padding: 16px 14px 16px 24px;

  &.withDescription {
    align-items: flex-start;
    margin-bottom: 8px;
  }
}

.headerTextWrapper {
  @extend %flex-row;
}

.headerTitleWrapper {
  @extend %flex-column;
  gap: 4px;
}

.title {
  @extend %header-4;
  margin: 0;
  flex-grow: 1;
}

.actionsWrapper {
  @extend %flex-row;
}

.sectionWrapper {
  flex-grow: 1;
  box-sizing: border-box;
  padding-bottom: 8px;
  height: calc(100% - 64px);

  &.withScroll {
    overflow-y: auto;
    height: initial;
  }
}
