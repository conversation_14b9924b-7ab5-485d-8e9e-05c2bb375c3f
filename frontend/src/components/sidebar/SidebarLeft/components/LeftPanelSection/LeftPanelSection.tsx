'use client';

import { useMemo } from 'react';
import { SectionAnimatedWrapper } from '../SectionAnimatedWrapper';
import { X } from '@phosphor-icons/react';
import styles from './LeftPanelSection.module.scss';
import classNames from 'classnames';
import { NavSidebar } from '../NavSidebar';
import { LeftPanelSections } from '@/types';
import { Button } from '@/components/ui/Button/index';
import { SVG_SIZE_M } from '@/constants';
import { useIsMobile } from '@/hooks/use-mobile';
import { useSidebar } from '@/components/ui/sidebar';
import { AddConversationButton } from '@/components/ui/AddConversationButton';
import { NavAgents } from '@/components/sidebar/nav-agents';
import * as React from 'react';
import { UserPanel } from '@/components/sidebar/SidebarLeft/components/UserPanel';

export const LeftPanelSection = () => {
  const { leftPanelSection, setIsLeftSidePanelOpen, isLeftSidePanelOpen } =
    useSidebar();

  const isMobile = useIsMobile();

  const sectionTitle: string | JSX.Element = useMemo(() => {
    switch (leftPanelSection) {
      case LeftPanelSections.THREAD_LIST:
        return 'Super Ninja history';
      case LeftPanelSections.USER_MENU:
        return 'Account';
      default:
        return '';
    }
  }, [leftPanelSection]);

  const section = useMemo(() => {
    switch (leftPanelSection) {
      case LeftPanelSections.THREAD_LIST:
        return <NavAgents />;
      case LeftPanelSections.USER_MENU:
        return <UserPanel />;
      default:
        return <></>;
    }
  }, [leftPanelSection]);

  const onCloseSection = () => {
    setIsLeftSidePanelOpen(!isLeftSidePanelOpen);
  };

  return (
    <SectionAnimatedWrapper>
      <div className={styles.root}>
        {isMobile && <NavSidebar />}

        <div className={styles.container}>
          <div className={styles.header}>
            <div className={styles.headerTextWrapper}>
              <div className={styles.headerTitleWrapper}>
                <p className={styles.title}>{sectionTitle}</p>
              </div>
            </div>

            <div className={styles.actionsWrapper}>
              {leftPanelSection !== LeftPanelSections.USER_MENU && (
                <AddConversationButton appearance="pencil-icon" />
              )}

              <Button
                color="transparent"
                shape="round"
                onClick={onCloseSection}
              >
                <X size={SVG_SIZE_M} />
              </Button>
            </div>
          </div>

          <div
            className={classNames(styles.sectionWrapper, {
              [styles.withScroll]:
                leftPanelSection === LeftPanelSections.USER_MENU,
            })}
          >
            {section}
          </div>
        </div>
      </div>
    </SectionAnimatedWrapper>
  );
};
