@use '@styles/index' as *;

.root {
  @extend %flex-column-center;

  box-sizing: border-box;
  width: var(--nj-left-panel-sidebar-width);
  justify-content: space-between;
  height: 100%;
  padding: 16px 8px;
  background-color: var(--nj-background-surface-primary);
  position: relative;
  z-index: 2;
  border-right: 1px solid transparent;

  &.withBorder {
    border-right: 1px solid var(--nj-foreground-border);
  }

  &.withShadow {
    box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.32);
  }

  @include from(tablet) {
    z-index: 32;
  }
}

.container {
  @extend %flex-column-center;

  gap: 16px;
}


.withRedDot {
  &::after {
    display: block;
    content: "";
    position: absolute;
    width: 8px;
    height: 8px;
    border-radius: 10px;
    right: 4px;
    top: 4px;
    background-color: var(--nj-accent-urgent);
  }
}

.bottomButton {
  margin: 0 10px;
}
