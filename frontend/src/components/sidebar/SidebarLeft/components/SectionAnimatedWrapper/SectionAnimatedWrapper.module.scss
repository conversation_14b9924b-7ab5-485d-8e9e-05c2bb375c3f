@use '@styles/index' as *;

.root {
  position: absolute;
  width: calc(
    var(--nj-left-navigation-width-mobile) + var(--nj-left-panel-sidebar-width)
  );
  top: 0;
  left: 0;
  z-index: var(--nj-left-panel-z-index);
  height: 100%;
  overflow: hidden;
  box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.32);

  @include from(tablet) {
    width: var(--nj-left-navigation-width-mobile);
    left: var(--nj-left-panel-sidebar-width);
  }

  @include from(desktop) {
    position: relative;
    left: initial;
    width: var(--nj-left-navigation-width);
    flex-shrink: 0;
  }
}
