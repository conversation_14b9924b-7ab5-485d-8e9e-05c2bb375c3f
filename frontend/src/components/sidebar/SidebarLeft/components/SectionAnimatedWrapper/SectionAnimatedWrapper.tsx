'use client';

import { ReactNode, useRef } from 'react';
import { CSSTransition } from 'react-transition-group';
import styles from './SectionAnimatedWrapper.module.scss';
import { ANIMATION_TIMEOUT_MIDDLE } from '@/constants';
import { useIsMobile } from '@/hooks/use-mobile';
import { useSidebar } from '@/components/ui/sidebar';

interface SectionAnimatedWrapperProps {
  children: ReactNode;
}

export const SectionAnimatedWrapper = ({
  children,
}: SectionAnimatedWrapperProps) => {
  const containerRef = useRef<HTMLDivElement>(null);

  const isMobile = useIsMobile();

  const { isLeftSidePanelOpen } = useSidebar();

  return (
    <CSSTransition
      in={isLeftSidePanelOpen}
      timeout={ANIMATION_TIMEOUT_MIDDLE}
      classNames={
        isMobile
          ? 'nj-animate-horizontal-appearance-ltr'
          : 'nj-animate-left-panel-width-reduce'
      }
      unmountOnExit
      nodeRef={containerRef}
    >
      <div ref={containerRef} className={styles.root}>
        {children}
      </div>
    </CSSTransition>
  );
};
