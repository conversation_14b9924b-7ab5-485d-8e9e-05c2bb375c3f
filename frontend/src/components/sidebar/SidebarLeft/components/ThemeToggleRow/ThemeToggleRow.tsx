'use client';

import styles from './ThemeToggleRow.module.scss';
import { Moon } from '@phosphor-icons/react';
import { SVG_SIZE_M } from '@/constants';
import { useTheme } from 'next-themes';
import { SwitchSimple } from '@/components/ui/SwitchSimple';

export const ThemeToggleRow = () => {
  const { resolvedTheme, setTheme } = useTheme();
  const isDarkTheme = resolvedTheme === 'dark';

  const handleChange = () => {
    setTheme(resolvedTheme === 'light' ? 'dark' : 'light');
  };

  return (
    <div className={styles.root}>
      <div className={styles.leftSide}>
        <Moon size={SVG_SIZE_M} />
        Dark
      </div>
      <SwitchSimple isChecked={isDarkTheme} onChange={handleChange} />
    </div>
  );
};
