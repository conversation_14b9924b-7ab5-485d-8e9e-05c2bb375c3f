'use client';

import cn from 'classnames';
import styles from './UserImage.module.scss';
import Image from 'next/image';
import { SVG_SIZE_XL } from '@/constants';
import * as React from 'react';
import { getInitials } from '@/utils';
import { useUserInfo } from '@/hooks/useUserInfo';

interface UserImageProps {
  size?: number;
}

export const UserImage = ({ size = SVG_SIZE_XL }: UserImageProps) => {
  const { name, avatar } = useUserInfo();

  const alt = getInitials(name);

  if (!!avatar) {
    return (
      <span className={styles.root}>
        <Image
          referrerPolicy="no-referrer"
          src={avatar}
          alt={alt}
          width={size}
          height={size}
        />
      </span>
    );
  }

  const style = {
    width: size,
    height: size,
  };

  return (
    <span className={cn(styles.root, styles.withInitials)} style={style}>
      <span data-testid="circle-icon" className={styles.initials}>
        {alt}
      </span>
    </span>
  );
};
