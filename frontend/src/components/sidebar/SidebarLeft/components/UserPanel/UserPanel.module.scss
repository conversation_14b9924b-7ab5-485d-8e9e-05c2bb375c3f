@use '@styles/index' as *;

.root {
  @extend %flex-column;
  justify-content: space-between;
  height: 100%;
}

.userInfoBlock {
  @extend %flex-column-center;
}

.nameWrapper {
  @extend %flex-column-center;
  gap: 0;
}

.userName {
  @extend %sub-header-2;
}

.userEmail {
  @extend %caption-1;
  color: var(--nj-foreground-secondary);
}

.footer {
  @extend %flex-column-center;
  gap: 0;
  padding: 16px;
}

.manageSubscriptionButton {
  @extend %button;
  @extend %flex-row;
  min-width: 80px;
  padding: 10px 16px;
  align-items: center;
  border-radius: 20px;
  border: 1px solid var(--nj-foreground-primary);
  margin: 16px auto;
}
