'use client';

import styles from './UserPanel.module.scss';
import { UserImage } from '@/components/sidebar/SidebarLeft/components/UserImage';
import { SVG_SIZE_XXL } from '@/constants';
import { useUserInfo } from '@/hooks/useUserInfo';
import { TextDivider } from '@/components/ui/TextDivider';
import { SignOutButton } from '@/components/ui/SignOutButton';
import { SuperNinjaLabel } from '@/components/sidebar/SidebarLeft/components/SuperNinjaLabel';
import { NEXT_PUBLIC_MY_NINJA_URL } from '@/lib/envs';
import { ThemeToggleRow } from '@/components/sidebar/SidebarLeft/components/ThemeToggleRow';

export const UserPanel = () => {
  const { name, email } = useUserInfo();

  return (
    <div className={styles.root}>
      <div className={styles.userInfoBlock}>
        <UserImage size={SVG_SIZE_XXL} />
        <div className={styles.nameWrapper}>
          <span className={styles.userName}>{name}</span>
          <span className={styles.userEmail}>{email}</span>
        </div>
        <SuperNinjaLabel />
        <ThemeToggleRow />
        <a
          href={`${NEXT_PUBLIC_MY_NINJA_URL}/manage-account/subscription`}
          className={styles.manageSubscriptionButton}
          target="_blank"
          rel="noopener noreferrer"
        >
          Manage subscription in Ninja
        </a>
      </div>
      <div className={styles.footer}>
        <TextDivider />
        <SignOutButton />
      </div>
    </div>
  );
};
