import React from 'react';
import { diffLines, diffChars } from 'diff';
import {
  FileDiff,
  CheckCircle,
  AlertTriangle,
  CircleDashed,
} from 'lucide-react';
import { ToolViewProps } from './types';
import {
  extractFilePath,
  extractStrReplaceContent,
  formatTimestamp,
} from './utils';
import { GenericToolView } from './GenericToolView';
import { cn } from '@/lib/utils';
import { normalizeNewlines } from '@/utils';
import { safeJsonParse } from '../utils';
import { ToolViewFooterStatus } from './components/ToolViewFooterStatus';

export function StrReplaceToolView({
  name = 'str-replace',
  assistantContent,
  toolContent,
  assistantTimestamp,
  toolTimestamp,
  isSuccess = true,
  isStreaming = false,
}: ToolViewProps) {
  const filePath = extractFilePath(assistantContent);
  const { oldStr, newStr } = extractStrReplaceContent(assistantContent);

  if (!oldStr || !newStr) {
    return (
      <GenericToolView
        name={name}
        assistantContent={assistantContent}
        toolContent={toolContent}
        assistantTimestamp={assistantTimestamp}
        toolTimestamp={toolTimestamp}
        isSuccess={isSuccess}
        isStreaming={isStreaming}
      />
    );
  }

  const generateInlineDiff = (oldStr: string, newStr: string) => {
    const lineDiff = diffLines(oldStr, newStr);
    const result: {
      type: 'added' | 'removed' | 'unchanged';
      parts: { text: string; highlight?: boolean }[];
    }[] = [];

    for (let i = 0; i < lineDiff.length; i++) {
      const part = lineDiff[i];

      if (part.added && i > 0 && lineDiff[i - 1]?.removed) {
        const removed = lineDiff[i - 1].value;
        const added = part.value;
        const charDiff = diffChars(removed, added);

        result.pop();

        result.push({
          type: 'removed',
          parts: charDiff
            .filter((d) => d.removed || (!d.added && !d.removed))
            .map((d) => ({ text: d.value, highlight: !!d.removed })),
        });

        result.push({
          type: 'added',
          parts: charDiff
            .filter((d) => d.added || (!d.added && !d.removed))
            .map((d) => ({ text: d.value, highlight: !!d.added })),
        });
      } else if (!part.added && !part.removed) {
        result.push({ type: 'unchanged', parts: [{ text: part.value }] });
      } else {
        result.push({
          type: part.added ? 'added' : 'removed',
          parts: [{ text: part.value }],
        });
      }
    }

    return result;
  };

  const decodedOldStr = safeJsonParse(`"${oldStr}"`, '');
  const decodedNewStr = safeJsonParse(`"${newStr}"`, '');
  const oldClean = normalizeNewlines(decodedOldStr);
  const newClean = normalizeNewlines(decodedNewStr);
  const diffParts = generateInlineDiff(oldClean, newClean);

  return (
    <div className="flex flex-col h-full">
      <div className="flex-1 overflow-auto">
        <div className="overflow-hidden h-full flex flex-col">
          <div className="flex items-center p-2 justify-between border-y bg-white dark:bg-zinc-950 py-3 px-6">
            <div className="flex items-center">
              <FileDiff className="h-4 w-4 mr-2 text-zinc-600 dark:text-zinc-400" />
              <span className="text-xs font-medium text-zinc-700 dark:text-zinc-300">
                String Replacement
              </span>
            </div>
          </div>

          <div className="px-6 py-2 border-b border-zinc-200 dark:border-zinc-800 flex items-center bg-white dark:bg-zinc-950">
            <code className="text-xs font-mono text-zinc-700 dark:text-zinc-300">
              {filePath || 'Unknown file'}
            </code>
          </div>

          {isStreaming ? (
            <div className="flex-1 bg-white dark:bg-zinc-950 flex items-center justify-center">
              <div className="text-center p-6">
                <CircleDashed className="h-8 w-8 mx-auto mb-3 text-blue-500 animate-spin" />
                <p className="text-sm font-medium text-zinc-700 dark:text-zinc-300">
                  Processing string replacement...
                </p>
                {filePath && (
                  <p className="text-xs mt-1 text-zinc-500 dark:text-zinc-400 font-mono">
                    {filePath}
                  </p>
                )}
              </div>
            </div>
          ) : (
            <div className="text-xs font-mono overflow-auto">
              {diffParts.map((line, i) => (
                <div
                  key={i}
                  className={cn(
                    'flex whitespace-pre-wrap',
                    line.type === 'removed'
                      ? 'bg-red-50 dark:bg-red-900/20'
                      : line.type === 'added'
                        ? 'bg-green-50 dark:bg-green-900/20'
                        : 'bg-white dark:bg-zinc-950',
                  )}
                >
                  <div className="px-3 py-1 flex-1">
                    {line.parts.map((part, j) => (
                      <span
                        key={j}
                        className={cn(
                          part.highlight &&
                            (line.type === 'added'
                              ? 'bg-green-200 dark:bg-green-700/40'
                              : 'bg-red-200 dark:bg-red-700/40'),
                        )}
                      >
                        {part.text}
                      </span>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      <ToolViewFooterStatus
        isStreaming={isStreaming}
        isSuccess={isSuccess}
        successMessage="Replacement applied successfully"
        failureMessage="Replacement failed"
        loadingMessage="Processing string replacement..."
      />
    </div>
  );
}
