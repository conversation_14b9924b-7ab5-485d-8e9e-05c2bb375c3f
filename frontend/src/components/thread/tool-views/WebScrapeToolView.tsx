import React from 'react';
import {
  Globe,
  CheckCircle,
  AlertTriangle,
  CircleDashed,
  ExternalLink,
} from 'lucide-react';
import { ToolViewProps } from './types';
import {
  extractCrawlUrl,
  extractWebpageContent,
  formatTimestamp,
  getToolTitle,
} from './utils';
import { GenericToolView } from './GenericToolView';
import { cn } from '@/lib/utils';
import { WebScrapeToolTextItem } from './WebScrapeToolTextItem';
import { fixBrokenMarkdownLinks, safeJsonParse } from '../utils';
import { safeRepairJson } from '@/utils';
import { ToolViewFooterStatus } from './components/ToolViewFooterStatus';
import { ExternalLinkIcon } from '@radix-ui/react-icons';
import { ArrowSquareOutIcon } from '@phosphor-icons/react';
import { SVG_SIZE_M } from '@/constants';

export function WebScrapeToolView({
  name = 'scrape-webpage',
  assistant<PERSON>ontent,
  toolContent,
  assistantTimestamp,
  toolTimestamp,
  isSuccess = true,
  isStreaming = false,
}: ToolViewProps) {
  const url = extractCrawlUrl(assistantContent);
  const webpageContent = extractWebpageContent(toolContent);
  const jsonWithFixesLinks = fixBrokenMarkdownLinks(webpageContent.text) || '';
  const preparedContent = safeJsonParse(jsonWithFixesLinks, []);
  const toolTitle = getToolTitle(name);

  if (!url) {
    return (
      <GenericToolView
        name={name}
        assistantContent={assistantContent}
        toolContent={toolContent}
        assistantTimestamp={assistantTimestamp}
        toolTimestamp={toolTimestamp}
        isSuccess={isSuccess}
        isStreaming={isStreaming}
      />
    );
  }

  // Format domain for display
  const formatDomain = (url: string): string => {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname.replace('www.', '');
    } catch (e) {
      return url;
    }
  };

  const domain = url ? formatDomain(url) : 'Unknown';

  return (
    <div className="flex flex-col h-full">
      <div className="flex-1 overflow-auto">
        <div className="overflow-hidden h-full flex flex-col">
          {/* Webpage Header */}
          <div className="flex items-center p-2 justify-between border-y bg-white dark:bg-zinc-950 py-1 px-6">
            <div className="flex items-center">
              <Globe className="h-4 w-4 mr-2 text-zinc-600 dark:text-zinc-400" />
              <span className="text-xs font-medium text-zinc-700 dark:text-zinc-300">
                {toolTitle}
              </span>
            </div>

            {!isStreaming && (
              <div className="flex items-center gap-2">
                <a
                  href={url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="p-3"
                >
                  <ArrowSquareOutIcon size={SVG_SIZE_M} />
                </a>
              </div>
            )}
          </div>

          {/* URL Bar */}
          <div className="px-3 py-2 border-b border-zinc-200 dark:border-zinc-800 bg-zinc-50 dark:bg-zinc-900">
            <code className="text-xs font-mono text-zinc-700 dark:text-zinc-300">
              {url}
            </code>
          </div>

          {/* Content */}
          {isStreaming ? (
            <div className="flex-1 bg-white dark:bg-zinc-950 flex items-center justify-center">
              <div className="text-center p-6">
                <CircleDashed className="h-8 w-8 mx-auto mb-3 text-blue-500 animate-spin" />
                <p className="text-sm font-medium text-zinc-700 dark:text-zinc-300">
                  Scraping webpage...
                </p>
                <p className="text-xs mt-1 text-zinc-500 dark:text-zinc-400">
                  Fetching content from {domain}
                </p>
              </div>
            </div>
          ) : (
            <div className="flex-1 overflow-auto bg-white dark:bg-zinc-950 font-mono text-sm">
              {preparedContent ? (
                <div className="p-3">
                  <div className="text-xs font-medium text-zinc-500 dark:text-zinc-400 mb-2">
                    Page Content
                  </div>
                  <div className="bg-zinc-50 dark:bg-zinc-900 border border-zinc-200 dark:border-zinc-800 rounded-md">
                    <div className="p-3 text-xs overflow-auto whitespace-pre-wrap text-zinc-800 dark:text-zinc-300 font-mono">
                      {Array.isArray(preparedContent) &&
                      preparedContent.length > 0
                        ? preparedContent.map((result, idx) => (
                            <WebScrapeToolTextItem
                              key={idx}
                              title={result?.Title}
                              url={result?.URL}
                              text={result?.Text}
                            />
                          ))
                        : 'No content extracted'}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="p-6 h-full flex items-center justify-center">
                  <div className="text-center">
                    <Globe className="h-6 w-6 mx-auto mb-2 text-zinc-400 dark:text-zinc-500" />
                    <p className="text-sm font-medium text-zinc-700 dark:text-zinc-300">
                      No content extracted
                    </p>
                    <p className="text-xs mt-1 text-zinc-500 dark:text-zinc-400">
                      The webpage might be restricted or empty
                    </p>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      <ToolViewFooterStatus
        isStreaming={isStreaming}
        isSuccess={isSuccess}
        toolTitle={toolTitle}
        loadingMessage={`Executing ${toolTitle.toLowerCase()}...`}
      />
    </div>
  );
}
