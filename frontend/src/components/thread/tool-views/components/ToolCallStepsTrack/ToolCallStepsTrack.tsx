'use client';

import {
  CaretLeftIcon,
  CaretLineRightIcon,
  CaretRightIcon,
} from '@phosphor-icons/react';

import { useIsMobile } from '@/hooks/use-mobile';
import { Slider } from '@/components/ui/Slider/Slider';
import { Button } from '@/components/ui/Button/Button';
import { SVG_SIZE_M } from '@/constants';

import styles from './ToolCallStepsTrack.module.scss';

interface ToolCallStepsTrackProps {
  totalCalls: number;
  currentIndex: number;
  onNavigate: (index: number) => void;
}

export const ToolCallStepsTrack = ({
  totalCalls,
  currentIndex,
  onNavigate,
}: ToolCallStepsTrackProps) => {
  const isMobile = useIsMobile();
  const isFirst = currentIndex <= 0;
  const isLast = currentIndex >= totalCalls - 1;

  const goToPrev = () => {
    if (!isFirst) onNavigate(currentIndex - 1);
  };

  const goToNext = () => {
    if (!isLast) onNavigate(currentIndex + 1);
  };

  const goToLast = () => {
    if (!isLast) onNavigate(totalCalls - 1);
  };

  const counter = (
    <div className={styles.totalCount}>
      {currentIndex + 1} / {totalCalls}
    </div>
  );

  if (isMobile) {
    return (
      <div className={styles.mobileWrapper}>
        <div className={styles.innerMobileContent}>
          <Button
            size="small"
            color="tertiary-neutral"
            disabled={isFirst}
            onClick={goToPrev}
          >
            <CaretLeftIcon size={SVG_SIZE_M} />
            <span>Previous</span>
          </Button>

          <span className={styles.totalCount}>{counter}</span>

          <Button
            size="small"
            color="tertiary-neutral"
            disabled={isLast}
            onClick={goToNext}
          >
            <span>Next</span>
            <CaretRightIcon size={SVG_SIZE_M} />
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.sliderWrapper}>
      <Slider
        min={0}
        max={totalCalls - 1}
        step={1}
        value={[currentIndex]}
        onValueChange={([newValue]) => onNavigate(newValue)}
      />

      <div className={styles.controlButtons}>
        <Button
          size="small"
          shape="round"
          color="transparent"
          disabled={isFirst}
          onClick={goToPrev}
        >
          <CaretLeftIcon size={SVG_SIZE_M} />
        </Button>

        {counter}

        <Button
          size="small"
          shape="round"
          color="transparent"
          disabled={isLast}
          onClick={goToNext}
        >
          <CaretRightIcon size={SVG_SIZE_M} />
        </Button>

        <Button
          size="small"
          shape="round"
          color="transparent"
          disabled={isLast}
          onClick={goToLast}
        >
          <CaretLineRightIcon size={SVG_SIZE_M} />
        </Button>
      </div>
    </div>
  );
};
