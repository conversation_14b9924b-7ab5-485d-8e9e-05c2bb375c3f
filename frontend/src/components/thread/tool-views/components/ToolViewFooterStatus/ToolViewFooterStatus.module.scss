@use '@styles/index' as *;

.root {
  padding: 16px 24px;
  border-top: 1px solid var(--nj-foreground-border);
}

.container {
  @extend %flex-between;
  @extend %caption-1;
  color: var(--nj-foreground-primary);
}

.status {
  @extend %flex-row;

  span {
    @extend %caption-1;
  }
}

.successIcon {
  color: var(--nj-accent-success);
}

.errorIcon {
  color: var(--nj-accent-urgent);
}

.loadingIcon {
  color: var(--nj-accent-primary);
  animation: infinite-spinner var(--nj-speed--5x) linear infinite;
}
