'use client';

import {
  CheckCircleIcon,
  CircleDashedIcon,
  WarningIcon,
} from '@phosphor-icons/react';

import styles from './ToolViewFooterStatus.module.scss';

interface ToolViewFooterStatusProps {
  isStreaming: boolean;
  isSuccess: boolean;
  operation?: string;
  toolTitle?: string;
  exitCode?: number | null;
  successMessage?: string;
  failureMessage?: string;
  loadingMessage?: string;
}

export const ToolViewFooterStatus = ({
  isStreaming,
  isSuccess,
  operation,
  toolTitle,
  exitCode = null,
  successMessage,
  failureMessage,
  loadingMessage,
}: ToolViewFooterStatusProps) => {
  const defaultSuccess = () => {
    if (successMessage) {
      return successMessage;
    }

    if (toolTitle) {
      return `${toolTitle} completed successfully`;
    }

    if (operation) {
      return `${operation} completed successfully`;
    }

    if (exitCode !== null) {
      return `Command completed successfully (exit code: ${exitCode})`;
    }

    return 'Completed successfully';
  };

  const defaultFailure = () => {
    if (failureMessage) {
      return failureMessage;
    }

    if (toolTitle) {
      return `${toolTitle} operation failed`;
    }

    if (operation) {
      return `${operation} failed`;
    }

    if (exitCode !== null) {
      return `Command failed with exit code ${exitCode}`;
    }

    return 'Execution failed';
  };

  const defaultLoading = () => {
    if (loadingMessage) {
      return loadingMessage;
    }

    if (toolTitle) {
      return `Executing ${toolTitle.toLowerCase()}...`;
    }

    if (operation) {
      return `Executing ${operation.toLowerCase()}...`;
    }

    return 'Processing...';
  };

  return (
    <div className={styles.root}>
      <div className={styles.container}>
        {!isStreaming && (
          <div className={styles.status}>
            {isSuccess ? (
              <CheckCircleIcon className={styles.successIcon} weight="fill" />
            ) : (
              <WarningIcon className={styles.errorIcon} weight="fill" />
            )}
            <span>{isSuccess ? defaultSuccess() : defaultFailure()}</span>
          </div>
        )}

        {isStreaming && (
          <div className={styles.status}>
            <CircleDashedIcon className={styles.loadingIcon} />
            <span>{defaultLoading()}</span>
          </div>
        )}
      </div>
    </div>
  );
};
