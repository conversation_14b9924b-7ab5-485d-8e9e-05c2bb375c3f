'use client';

import { NotePencil } from '@phosphor-icons/react';
import lightIcon from '@/images/new_chat_icon_light.svg';
import darkIcon from '@/images/new_chat_icon_dark.svg';
import styles from './AddConversationButton.module.scss';
import { Button } from '@/components/ui/Button/index';
import { AppRoutes, SVG_SIZE_M } from '@/constants';
import { useTheme } from 'next-themes';
import Image from 'next/image';
import { redirect } from 'next/navigation';

interface AddConversationButtonProps {
  appearance: 'pencil-icon' | 'gradient-plus-button';
}

export const AddConversationButton = ({
  appearance,
}: AddConversationButtonProps) => {
  const { resolvedTheme } = useTheme();

  const handleCreateNewChat = async () => {
    redirect(AppRoutes.DASHBOARD);
  };

  if (appearance === 'pencil-icon') {
    return (
      <Button color="transparent" shape="round" onClick={handleCreateNewChat}>
        <NotePencil size={SVG_SIZE_M} />
      </Button>
    );
  }

  if (appearance === 'gradient-plus-button') {
    return (
      <Button
        color="transparent"
        shape="round"
        onClick={handleCreateNewChat}
        className={styles.gradientIconButton}
        tooltipContent="New chat"
      >
        <Image
          referrerPolicy="no-referrer"
          src={resolvedTheme === 'light' ? lightIcon : darkIcon}
          alt="Logo"
          width={44}
          height={44}
        />
      </Button>
    );
  }

  return null;
};
