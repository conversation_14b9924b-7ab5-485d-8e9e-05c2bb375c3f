'use client';

import { SignOut } from '@phosphor-icons/react';
import styles from './SignOutButton.module.scss';
import { Button } from '@/components/ui/Button/index';
import { SVG_SIZE_M } from '@/constants';
import { signOut as cognitoSignOut } from 'aws-amplify/auth';
import { redirect } from 'next/navigation';

export const SignOutButton = () => {
  const handleLogout = async () => {
    await cognitoSignOut();
    return redirect('/login');
  };

  return (
    <Button color="transparent" onClick={handleLogout} className={styles.root}>
      <SignOut size={SVG_SIZE_M} />
      <span className={styles.buttonText}>Sign out</span>
    </Button>
  );
};
