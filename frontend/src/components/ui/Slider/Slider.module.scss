@use '@styles/index' as *;

.root {
  @extend %flex-center;

  width: 100%;
  position: relative;
  touch-action: none;
  user-select: none;

  &[data-orientation='vertical'] {
    height: 100%;
    min-height: 11rem;
    width: auto;
    flex-direction: column;
  }
}

.sliderTrack {
  position: relative;
  flex-grow: 1;
  overflow: visible;
  border-radius: 100px;
  background-color: var(--nj-background-surface-primary);
  height: 4px;
  width: 100%;

  &[data-orientation='vertical'] {
    height: 100%;
    width: 6px;
  }
}

.sliderRange {
  position: absolute;
  background-color: var(--nj-foreground-primary);
  height: 100%;
  border-radius: 100px;
  top: 0;
  left: 0;

  &[data-orientation='vertical'] {
    width: 100%;
    height: auto;
  }
}

.sliderThumb {
  @extend %flex-center;

  width: 16px;
  height: 16px;
  background-color: var(--nj-foreground-primary);
  border: 1px solid var(--nj-foreground-primary);
  border-radius: 100px;
  box-shadow: 0px 2px 4px 0px var(--nj-shadow);
  transition: box-shadow 0.2s;
  cursor: pointer;

  &:disabled {
    opacity: var(--nj-opacity-primary);
    pointer-events: none;
  }
}
