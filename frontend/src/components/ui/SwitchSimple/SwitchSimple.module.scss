@use '@styles/index' as *;

.root {
  box-sizing: border-box;
  position: relative;
  display: flex;
  width: 40px;
  padding: 2px;
  height: 24px;
  border-radius: 12px;
  background-color: var(--nj-foreground-secondary);
  flex-shrink: 0;

  &.switchOn {
    background-color: var(--nj-background-surface-selected);

    .slider {
      transform: translateX(calc(100% - 4px));
      background-color: var(--nj-accent-secondary);
    }
  }

  &.disabled {
    opacity: var(--nj-opacity-secondary);

    .label {
      cursor: not-allowed;
    }
  }
}

.input {
  position: absolute;
  opacity: 0;
  height: 0;
  width: 0;
}

.label {
  width: 100%;
  cursor: pointer;
}

.slider {
  box-sizing: border-box;
  border-radius: 10px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--nj-foreground-primary-muted);
  color: var(--foreground-primary-muted);
  color: purple;
  transform: translateX(0);
  transition: transform var(--nj-speed--x);

  & > svg {
    flex-shrink: 0;
  }
}
