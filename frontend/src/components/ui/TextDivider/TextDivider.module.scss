@use '@styles/index' as *;

.root {
  @extend %caption-2;
  @extend %flex-row;

  text-transform: uppercase;
  color: var(--nj-foreground-secondary);

  &::before,
  &::after {
    display: block;
    content: "";
    height: 1px;
    background-color: var(--nj-foreground-border);
    flex-grow: 1;
  }
}

.divider {
  width: 100%;
  border: none;
  border-top: 1px solid var(--nj-foreground-border);
  margin: 0;

  &.withMargin {
    margin: 8px 0;
  }

  &.dashed {
    border-top: 1px dashed var(--nj-foreground-border);
  }
}
