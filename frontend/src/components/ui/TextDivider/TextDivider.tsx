'use client';

import classNames from 'classnames';
import styles from './TextDivider.module.scss';

interface TextDividerProps {
  text?: string;
  withMargin?: boolean;
  type?: 'dashed' | 'solid';
}

export const TextDivider = ({
  text,
  withMargin = true,
  type = 'solid',
}: TextDividerProps) => {
  return text ? (
    <span className={styles.root}>{text}</span>
  ) : (
    <hr
      className={classNames(styles.divider, {
        [styles.withMargin]: withMargin,
        [styles.dashed]: type === 'dashed',
      })}
    />
  );
};
