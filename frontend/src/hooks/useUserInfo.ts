'use client';

import { useEffect, useMemo, useState } from 'react';
import { getUserFromAmplifySession } from '@/lib/utils/amplify-client-utils';

export const useUserInfo = () => {
  const [user, setUser] = useState<{
    name: string;
    email: string;
    avatar: string;
  }>({
    name: 'Loading...',
    email: '<EMAIL>',
    avatar: '',
  });

  // Fetch user data
  useEffect(() => {
    const fetchUserData = async () => {
      const user = await getUserFromAmplifySession();

      if (user) {
        setUser(user);
      }
    };

    fetchUserData();
  }, []);

  return useMemo(
    () => ({
      name: user?.name || '',
      email: user?.email || '',
      avatar: user?.avatar || '',
    }),
    [user],
  );
};
