'use client';

import { fetchAuthSession } from '@aws-amplify/core';

export type UserData = {
  ninja_user_id: string;
  id: string;
  name: string;
  email: string;
  avatar: string;
};

export const getUserFromAmplifySession = async () => {
  try {
    const currentSession = await fetchAuthSession();
    return {
      ninja_user_id:
        (currentSession?.tokens?.accessToken?.payload[
          'custom:ninja_user_id'
        ] as string) || '',
      id: (currentSession?.tokens?.accessToken?.payload?.sub as string) || '',
      name:
        (currentSession?.tokens?.idToken?.payload['given_name'] as string) ||
        'User',
      email:
        (currentSession?.tokens?.idToken?.payload['email'] as string) || '',
      avatar: '',
    } as UserData;
  } catch (error) {
    console.error('Error fetching user from Amplify session:', error);
    return null;
  }
};
