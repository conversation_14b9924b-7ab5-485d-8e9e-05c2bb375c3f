export enum KeyCodes {
  SPACE = ' ',
  ENTER = 'Enter',
  DELETE = 'Delete',
  BACKSPACE = 'Backspace',
  BACKSPACESHIFT = 'BackspaceShift',
  ESCAPE = 'Escape',
  AT = '@',
  HASH = '#',
  SLASH = '/',
  ARROW_DOWN = 'ArrowDown',
  ARROW_UP = 'ArrowUp',
}

export type ExampleItem = {
  id: number;
  title: string;
  subtitle: string;
  query: string;
  exampleUrl: string;
  imageUrl?: string;
};

export enum LeftPanelSections {
  THREAD_LIST = 'threadList',
  USER_MENU = 'userMenu',
}
