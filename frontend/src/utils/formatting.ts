import { jsonrepair as json<PERSON><PERSON>air } from 'jsonrepair';
import { lexer } from 'marked';
import type { Tokens, Token } from 'marked';

export const normalizeNewlines = (input: string): string => {
  return input.replace(/\\n/g, '\n');
};

export const safeRepairJson = (input: string): string => {
  try {
    const repaired = jsonRepair(input);
    return repaired;
  } catch (error) {
    return input;
  }
};

export const getHtmlContentForCopyToClipboard = (content: string) => {
  return `<!DOCTYPE html>
      <html>
        <head>
          <style>
            table {
              border-collapse: collapse;
              width: 100%;
            }
            th, td {
              border: 1px solid #ccc;
              padding: 8px;
              text-align: left;
            }
          </style>
        </head>
        <body>${content}</body>
      </html>`;
};

export const getPlainTextContentForCopyToClipboard = (
  markdown: string,
): string => {
  const tokens: Token[] = lexer(markdown);
  let result = '';

  const renderTokens = (tokens: Token[], indent = ''): void => {
    for (const token of tokens) {
      switch (token.type) {
        case 'heading': {
          const t = token as Tokens.Heading;
          result += `\n${t.depth === 1 ? '' : indent}${t.text}\n`;
          break;
        }

        case 'paragraph': {
          const t = token as Tokens.Paragraph;
          result += `\n${indent}${t.text}\n`;
          break;
        }

        case 'list': {
          const t = token as Tokens.List;
          const listContent = t.items
            .map((item: Tokens.ListItem, i) => {
              const prefix = t.ordered ? `${i + 1}.` : '•';
              return `${indent}${prefix} ${item.text}\n`;
            })
            .join('');
          result += listContent;
          break;
        }

        case 'table': {
          const t = token as Tokens.Table;

          const extractText = (cell: unknown): string => {
            if (typeof cell === 'string') {
              return cell;
            }

            if (typeof cell === 'object' && cell && 'text' in cell) {
              return String((cell as { text: string }).text);
            }

            return String(cell);
          };

          const header = t.header.map(extractText).join('\t');
          const rows = t.rows.map((row) => row.map(extractText).join('\t'));

          result += `\n${header}\n${rows.join('\n')}\n`;
          break;
        }

        case 'code': {
          const t = token as Tokens.Code;
          result += `\n${t.text}\n`;
          break;
        }

        case 'hr':
          result += '\n⸻\n';
          break;

        case 'blockquote': {
          const t = token as Tokens.Blockquote;
          result += `\n> ${t.text}\n`;
          break;
        }

        case 'space':
          result += '\n';
          break;

        default: {
          if ('text' in token && typeof token.text === 'string') {
            result += `\n${token.text}\n`;
          }
        }
      }
    }
  };

  renderTokens(tokens);

  return result.trim();
};
