@keyframes infinite-spinner {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes blinking {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

// nj-animate-horizontal-appearance-ltr
.nj-animate-horizontal-appearance-ltr-enter {
  transform: translateX(-100%);
}

.nj-animate-horizontal-appearance-ltr-enter-active {
  transform: translateX(0);
  transition: transform var(--nj-speed--xxl);
}

.nj-animate-horizontal-appearance-ltr-exit {
  transform: translateX(0);
}

.nj-animate-horizontal-appearance-ltr-exit-active {
  transform: translateX(-100%);
  transition: transform var(--nj-speed--xxl);
}

// nj-animate-left-panel-width-reduce
.nj-animate-left-panel-width-reduce-enter {
  max-width: 0;
}

.nj-animate-left-panel-width-reduce-enter-active {
  max-width: var(--nj-left-navigation-width);
  transition: max-width var(--nj-speed--xxl);
}

.nj-animate-left-panel-width-reduce-exit {
  max-width: var(--nj-left-navigation-width);
}

.nj-animate-left-panel-width-reduce-exit-active {
  max-width: 0;
  transition: max-width var(--nj-speed--xxl);
}
