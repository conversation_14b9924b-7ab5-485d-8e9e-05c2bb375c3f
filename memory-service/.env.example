# Environment Configuration
ENV_MODE=local
LOG_LEVEL=INFO

# Service Configuration
HOST=0.0.0.0
PORT=8001

# Vector Database Configuration
VECTOR_DB_TYPE=chroma  # Options: chroma, qdrant
QDRANT_HOST=localhost
QDRANT_PORT=6333
QDRANT_API_KEY=

# LLM Configuration (at least one is required)
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# CORS Configuration (comma-separated list)
CORS_ORIGINS=http://localhost:3000,http://localhost:8000

# Mem0 Configuration (optional)
MEM0_API_KEY=
