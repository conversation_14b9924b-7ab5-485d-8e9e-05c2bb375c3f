# Memory Service

A standalone FastAPI service for managing AI agent memories using [mem0ai](https://github.com/mem0ai/mem0). This service provides a REST API for storing, searching, and managing user-specific memories that can be used to enhance AI agent interactions.

## Features

- **Memory Storage**: Store user-specific memories with metadata
- **Semantic Search**: Search for relevant memories using natural language queries
- **Conversation Memory**: Extract and store memories from conversation exchanges
- **Multiple Vector Stores**: Support for ChromaDB (default) and Qdrant
- **Multiple LLM Providers**: Support for OpenAI and Anthropic models
- **REST API**: Full REST API with automatic documentation
- **Docker Support**: Easy deployment with Docker and docker-compose

## Quick Start

### Using Docker Compose (Recommended)

1. Clone the repository and navigate to the memory-service directory
2. Copy the environment file and configure your API keys:
   ```bash
   cp .env.example .env
   # Edit .env with your API keys
   ```

3. Start the service:
   ```bash
   docker-compose up -d
   ```

4. The service will be available at `http://localhost:8001`
5. API documentation is available at `http://localhost:8001/docs`

### Local Development

1. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Set up environment variables:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. Run the service:
   ```bash
   cd src
   python main.py
   ```

## Configuration

The service can be configured using environment variables:

| Variable | Description | Default |
|----------|-------------|---------|
| `ENV_MODE` | Environment mode (local/staging/production) | `local` |
| `HOST` | Service host | `0.0.0.0` |
| `PORT` | Service port | `8001` |
| `LOG_LEVEL` | Logging level | `INFO` |
| `VECTOR_DB_TYPE` | Vector database type (chroma/qdrant) | `chroma` |
| `OPENAI_API_KEY` | OpenAI API key | - |
| `ANTHROPIC_API_KEY` | Anthropic API key | - |
| `QDRANT_HOST` | Qdrant host (if using Qdrant) | `localhost` |
| `QDRANT_PORT` | Qdrant port | `6333` |
| `CORS_ORIGINS` | Allowed CORS origins (comma-separated) | `http://localhost:3000,http://localhost:8000` |

## API Endpoints

### Health Check
- `GET /health` - Service health check

### Memory Operations
- `POST /api/memories/search` - Search for relevant memories
- `POST /api/memories/add` - Add a new memory
- `POST /api/memories/conversation` - Add memory from conversation
- `POST /api/memories/list` - Get all memories for a user
- `DELETE /api/memories/{memory_id}` - Delete a specific memory

### Example Usage

#### Search Memories
```bash
curl -X POST "http://localhost:8001/api/memories/search" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "What programming languages does the user prefer?",
    "user_id": "user123",
    "limit": 10,
    "threshold": 0.7
  }'
```

#### Add Memory
```bash
curl -X POST "http://localhost:8001/api/memories/add" \
  -H "Content-Type: application/json" \
  -d '{
    "content": "User prefers Python for backend development",
    "user_id": "user123",
    "metadata": {"category": "preferences"}
  }'
```

## Vector Database Options

### ChromaDB (Default)
- No additional setup required
- Data stored locally in `./chroma_db` directory
- Good for development and small deployments

### Qdrant
- Requires Qdrant server
- Better performance for large-scale deployments
- Can be started with docker-compose (uncomment qdrant service)

## Deployment

### Docker
```bash
docker build -t memory-service .
docker run -p 8001:8001 --env-file .env memory-service
```

### Production Considerations
- Use external vector database (Qdrant Cloud, etc.)
- Set up proper logging and monitoring
- Configure CORS origins for your domain
- Use environment-specific configuration
- Set up health checks and auto-restart policies

## Development

### Project Structure
```
memory-service/
├── src/
│   ├── main.py           # FastAPI application
│   ├── memory_service.py # Core memory service logic
│   ├── models.py         # Pydantic models
│   ├── config.py         # Configuration management
│   └── logger.py         # Logging setup
├── tests/                # Test files
├── docs/                 # Documentation
├── Dockerfile           # Docker configuration
├── docker-compose.yml   # Docker compose setup
├── requirements.txt     # Python dependencies
└── README.md           # This file
```

### Running Tests
```bash
# Install test dependencies
pip install pytest pytest-asyncio

# Run tests
pytest tests/
```

## License

This project is part of the ninja-suna-manus repository.
