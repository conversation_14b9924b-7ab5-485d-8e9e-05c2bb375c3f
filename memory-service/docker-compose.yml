version: '3.8'

services:
  memory-service:
    build: .
    ports:
      - "8001:8001"
    environment:
      - ENV_MODE=local
      - LOG_LEVEL=INFO
      - HOST=0.0.0.0
      - PORT=8001
      - VECTOR_DB_TYPE=chroma
      # Add your API keys here or use .env file
      # - OPENAI_API_KEY=${OPENAI_API_KEY}
      # - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
    volumes:
      - ./src:/app/src
      - chroma_data:/app/chroma_db
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  chroma_data:
  # qdrant_data:
