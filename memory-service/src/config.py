"""
Configuration management for the Memory Service.

This module provides a minimal configuration setup for the standalone memory service.
"""
import os
from enum import Enum
from typing import Optional

from pydantic import field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class EnvMode(Enum):
    """Environment mode enumeration."""
    LOCAL = "local"
    STAGING = "staging"
    PRODUCTION = "production"


class MemoryServiceConfig(BaseSettings):
    """
    Configuration for the Memory Service.
    
    This class loads environment variables and provides type checking and validation
    for the memory service specific configuration.
    """
    
    model_config = SettingsConfigDict(extra="ignore", env_file=".env")
    
    # Environment mode
    ENV_MODE: EnvMode = EnvMode.LOCAL
    
    # Service configuration
    HOST: str = "0.0.0.0"
    PORT: int = 8001
    
    # Memory service specific configuration
    MEM0_API_KEY: Optional[str] = None
    
    # Vector database configuration (for mem0)
    VECTOR_DB_TYPE: str = "chroma"  # chroma, qdrant, etc.
    QDRANT_HOST: Optional[str] = None
    QDRANT_PORT: int = 6333
    QDRANT_API_KEY: Optional[str] = None
    
    # LLM configuration for mem0
    OPENAI_API_KEY: Optional[str] = None
    ANTHROPIC_API_KEY: Optional[str] = None
    
    # Logging configuration
    LOG_LEVEL: str = "INFO"
    
    # CORS configuration
    CORS_ORIGINS: list[str] = ["http://localhost:3000", "http://localhost:8000"]
    
    @field_validator("ENV_MODE", mode="before")
    def _validate_env_mode(cls, value: str) -> EnvMode:
        try:
            return EnvMode(value)
        except ValueError:
            return EnvMode.LOCAL
    
    @field_validator("CORS_ORIGINS", mode="before")
    def _parse_cors_origins(cls, value):
        if isinstance(value, str):
            return [origin.strip() for origin in value.split(",")]
        return value


# Global configuration instance
config = MemoryServiceConfig()
