"""
Logging configuration for the Memory Service.

This module provides a simple logging setup that doesn't depend on external libraries.
"""
import logging
import sys
from typing import Optional

from config import config


def setup_logger(name: Optional[str] = None, level: Optional[str] = None) -> logging.Logger:
    """
    Set up a logger with the specified name and level.
    
    Args:
        name: Logger name (defaults to 'memory-service')
        level: Log level (defaults to config.LOG_LEVEL)
        
    Returns:
        Configured logger instance
    """
    logger_name = name or "memory-service"
    log_level = level or config.LOG_LEVEL
    
    # Create logger
    logger = logging.getLogger(logger_name)
    
    # Avoid adding multiple handlers if logger already exists
    if logger.handlers:
        return logger
    
    # Set log level
    logger.setLevel(getattr(logging, log_level.upper(), logging.INFO))
    
    # Create console handler
    handler = logging.StreamHandler(sys.stdout)
    handler.setLevel(getattr(logging, log_level.upper(), logging.INFO))
    
    # Create formatter
    formatter = logging.Formatter(
        fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    handler.setFormatter(formatter)
    
    # Add handler to logger
    logger.addHandler(handler)
    
    return logger


# Global logger instance
logger = setup_logger()
