"""
Memory Service FastAPI Application.

This is a standalone FastAPI service for managing agent memories using mem0ai.
"""
from datetime import datetime, timezone
from typing import List

from fastapi import Fast<PERSON><PERSON>, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON><PERSON><PERSON>ponse

from config import config
from logger import logger
from memory_service import memory_service
from models import (
    ConversationMemoryRequest,
    ErrorResponse,
    HealthResponse,
    Memory,
    MemoryAddRequest,
    MemoryDeleteRequest,
    MemoryGetRequest,
    MemoryListResponse,
    MemoryOperationResponse,
    MemorySearchRequest,
    MemorySearchResponse,
)

# Create FastAPI application
app = FastAPI(
    title="Memory Service",
    description="Standalone memory service for AI agents using mem0ai",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=config.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["Content-Type", "Authorization"],
)


@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler."""
    logger.error(f"Unhandled exception: {str(exc)}")
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content=ErrorResponse(
            error="Internal server error",
            detail=str(exc) if config.ENV_MODE.value != "production" else None
        ).model_dump()
    )


@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint."""
    return HealthResponse(
        status="healthy",
        timestamp=datetime.now(timezone.utc).isoformat(),
        version="1.0.0"
    )


@app.post("/api/memories/search", response_model=MemorySearchResponse)
async def search_memories(request: MemorySearchRequest):
    """
    Search for relevant memories for a user based on query.
    """
    try:
        memories_data = await memory_service.search_memories(
            query=request.query,
            user_id=request.user_id,
            limit=request.limit,
            threshold=request.threshold
        )
        
        memories = [
            Memory(
                id=mem.get("id", ""),
                content=mem.get("content", ""),
                score=mem.get("score"),
                metadata=mem.get("metadata", {})
            )
            for mem in memories_data
        ]
        
        return MemorySearchResponse(
            memories=memories,
            total=len(memories)
        )
        
    except Exception as e:
        logger.error(f"Error searching memories: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to search memories: {str(e)}"
        )


@app.post("/api/memories/add", response_model=MemoryOperationResponse)
async def add_memory(request: MemoryAddRequest):
    """
    Add a new memory for a user.
    """
    try:
        success = await memory_service.add_memory(
            content=request.content,
            user_id=request.user_id,
            metadata=request.metadata
        )
        
        if success:
            return MemoryOperationResponse(
                success=True,
                message="Memory added successfully"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to add memory"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error adding memory: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to add memory: {str(e)}"
        )


@app.post("/api/memories/conversation", response_model=MemoryOperationResponse)
async def add_conversation_memory(request: ConversationMemoryRequest):
    """
    Add memory from a conversation exchange.
    """
    try:
        success = await memory_service.add_conversation_memory(
            user_message=request.user_message,
            assistant_response=request.assistant_response,
            user_id=request.user_id,
            thread_id=request.thread_id,
            model_name=request.model_name
        )

        if success:
            return MemoryOperationResponse(
                success=True,
                message="Conversation memory added successfully"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to add conversation memory"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error adding conversation memory: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to add conversation memory: {str(e)}"
        )


@app.post("/api/memories/list", response_model=MemoryListResponse)
async def get_user_memories(request: MemoryGetRequest):
    """
    Get all memories for a user.
    """
    try:
        memories_data = await memory_service.get_user_memories(
            user_id=request.user_id,
            limit=request.limit
        )

        memories = [
            Memory(
                id=mem.get("id", ""),
                content=mem.get("memory", mem.get("content", "")),
                metadata=mem.get("metadata", {})
            )
            for mem in memories_data
        ]

        return MemoryListResponse(
            memories=memories,
            total=len(memories)
        )

    except Exception as e:
        logger.error(f"Error getting user memories: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get user memories: {str(e)}"
        )


@app.delete("/api/memories/{memory_id}", response_model=MemoryOperationResponse)
async def delete_memory(memory_id: str, user_id: str):
    """
    Delete a specific memory.
    """
    try:
        success = await memory_service.delete_memory(
            memory_id=memory_id,
            user_id=user_id
        )

        if success:
            return MemoryOperationResponse(
                success=True,
                message="Memory deleted successfully"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete memory"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting memory: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete memory: {str(e)}"
        )


if __name__ == "__main__":
    import uvicorn

    logger.info(f"Starting Memory Service on {config.HOST}:{config.PORT}")
    uvicorn.run(
        "main:app",
        host=config.HOST,
        port=config.PORT,
        reload=config.ENV_MODE.value == "local",
        log_level=config.LOG_LEVEL.lower()
    )
