"""
Pydantic models for the Memory Service API.
"""
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class MemorySearchRequest(BaseModel):
    """Request model for searching memories."""
    query: str = Field(..., description="The search query to find relevant memories")
    user_id: str = Field(..., description="The user ID to search memories for")
    limit: int = Field(default=10, ge=1, le=100, description="Maximum number of memories to return")
    threshold: float = Field(default=0.7, ge=0.0, le=1.0, description="Minimum relevance threshold")


class MemoryAddRequest(BaseModel):
    """Request model for adding a memory."""
    content: str = Field(..., description="The memory content to store")
    user_id: str = Field(..., description="The user ID to associate the memory with")
    metadata: Optional[Dict[str, Any]] = Field(default=None, description="Optional metadata to store with the memory")


class ConversationMemoryRequest(BaseModel):
    """Request model for adding conversation memory."""
    user_message: str = Field(..., description="The user's message")
    assistant_response: str = Field(..., description="The assistant's response")
    user_id: str = Field(..., description="The user ID")
    thread_id: Optional[str] = Field(default=None, description="Optional thread ID for context")
    model_name: Optional[str] = Field(default=None, description="Optional model name used")


class MemoryGetRequest(BaseModel):
    """Request model for getting user memories."""
    user_id: str = Field(..., description="The user ID to get memories for")
    limit: int = Field(default=50, ge=1, le=200, description="Maximum number of memories to return")


class MemoryDeleteRequest(BaseModel):
    """Request model for deleting a memory."""
    memory_id: str = Field(..., description="The ID of the memory to delete")
    user_id: str = Field(..., description="The user ID for verification")


class Memory(BaseModel):
    """Memory model for API responses."""
    id: str = Field(..., description="Memory ID")
    content: str = Field(..., description="Memory content")
    score: Optional[float] = Field(default=None, description="Relevance score (for search results)")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Memory metadata")


class MemorySearchResponse(BaseModel):
    """Response model for memory search."""
    memories: List[Memory] = Field(..., description="List of relevant memories")
    total: int = Field(..., description="Total number of memories found")


class MemoryListResponse(BaseModel):
    """Response model for listing user memories."""
    memories: List[Memory] = Field(..., description="List of user memories")
    total: int = Field(..., description="Total number of memories")


class MemoryOperationResponse(BaseModel):
    """Response model for memory operations (add, delete)."""
    success: bool = Field(..., description="Whether the operation was successful")
    message: str = Field(..., description="Operation result message")


class HealthResponse(BaseModel):
    """Response model for health check."""
    status: str = Field(..., description="Service status")
    timestamp: str = Field(..., description="Current timestamp")
    version: str = Field(default="1.0.0", description="Service version")


class ErrorResponse(BaseModel):
    """Response model for errors."""
    error: str = Field(..., description="Error message")
    detail: Optional[str] = Field(default=None, description="Detailed error information")
