"""
Basic tests for the Memory Service API.
"""
import pytest
from fastapi.testclient import TestClient

from src.main import app

client = TestClient(app)


def test_health_check():
    """Test the health check endpoint."""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"
    assert "timestamp" in data
    assert "version" in data


def test_search_memories():
    """Test the search memories endpoint."""
    response = client.post(
        "/api/memories/search",
        json={
            "query": "test query",
            "user_id": "test_user",
            "limit": 5,
            "threshold": 0.5
        }
    )
    assert response.status_code == 200
    data = response.json()
    assert "memories" in data
    assert "total" in data
    assert isinstance(data["memories"], list)


def test_add_memory():
    """Test the add memory endpoint."""
    response = client.post(
        "/api/memories/add",
        json={
            "content": "Test memory content",
            "user_id": "test_user",
            "metadata": {"test": "true"}
        }
    )
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert "message" in data


def test_add_conversation_memory():
    """Test the add conversation memory endpoint."""
    response = client.post(
        "/api/memories/conversation",
        json={
            "user_message": "Hello, I like Python programming",
            "assistant_response": "That's great! Python is a versatile language.",
            "user_id": "test_user",
            "thread_id": "thread123"
        }
    )
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert "message" in data


def test_get_user_memories():
    """Test the get user memories endpoint."""
    response = client.post(
        "/api/memories/list",
        json={
            "user_id": "test_user",
            "limit": 10
        }
    )
    assert response.status_code == 200
    data = response.json()
    assert "memories" in data
    assert "total" in data
    assert isinstance(data["memories"], list)


def test_invalid_search_request():
    """Test search with invalid request data."""
    response = client.post(
        "/api/memories/search",
        json={
            "query": "",  # Empty query
            "user_id": "test_user"
        }
    )
    assert response.status_code == 422  # Validation error


def test_invalid_add_request():
    """Test add memory with invalid request data."""
    response = client.post(
        "/api/memories/add",
        json={
            "content": "",  # Empty content
            "user_id": "test_user"
        }
    )
    assert response.status_code == 422  # Validation error
