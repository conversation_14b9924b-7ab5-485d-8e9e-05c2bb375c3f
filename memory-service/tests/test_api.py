"""
Basic tests for the Memory Service API.

These tests make actual HTTP requests to a running memory service instance.
Make sure the service is running on localhost:8001 before running these tests.
"""
import pytest
import httpx

# Base URL for the memory service
BASE_URL = "http://localhost:8001"


@pytest.mark.asyncio
async def test_health_check():
    """Test the health check endpoint."""
    async with httpx.AsyncClient() as client:
        response = await client.get(f"{BASE_URL}/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "timestamp" in data
        assert "version" in data


@pytest.mark.asyncio
async def test_search_memories():
    """Test the search memories endpoint."""
    async with httpx.AsyncClient() as client:
        response = await client.post(
            f"{BASE_URL}/api/memories/search",
            json={
                "query": "test query",
                "user_id": "test_user",
                "limit": 5,
                "threshold": 0.5
            }
        )
        assert response.status_code == 200
        data = response.json()
        assert "memories" in data
        assert "total" in data
        assert isinstance(data["memories"], list)


@pytest.mark.asyncio
async def test_add_memory():
    """Test the add memory endpoint."""
    async with httpx.AsyncClient() as client:
        response = await client.post(
            f"{BASE_URL}/api/memories/add",
            json={
                "content": "Test memory content",
                "user_id": "test_user",
                "metadata": {"test": "true"}
            }
        )
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "message" in data


@pytest.mark.asyncio
async def test_add_conversation_memory():
    """Test the add conversation memory endpoint."""
    async with httpx.AsyncClient() as client:
        response = await client.post(
            f"{BASE_URL}/api/memories/conversation",
            json={
                "user_message": "Hello, I like Python programming",
                "assistant_response": "That's great! Python is a versatile language.",
                "user_id": "test_user",
                "thread_id": "thread123"
            }
        )
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "message" in data


@pytest.mark.asyncio
async def test_get_user_memories():
    """Test the get user memories endpoint."""
    async with httpx.AsyncClient() as client:
        response = await client.post(
            f"{BASE_URL}/api/memories/list",
            json={
                "user_id": "test_user",
                "limit": 10
            }
        )
        assert response.status_code == 200
        data = response.json()
        assert "memories" in data
        assert "total" in data
        assert isinstance(data["memories"], list)


@pytest.mark.asyncio
async def test_invalid_search_request():
    """Test search with invalid request data."""
    async with httpx.AsyncClient() as client:
        response = await client.post(
            f"{BASE_URL}/api/memories/search",
            json={
                "query": "",  # Empty query
                "user_id": "test_user"
            }
        )
        assert response.status_code == 422  # Validation error


@pytest.mark.asyncio
async def test_invalid_add_request():
    """Test add memory with invalid request data."""
    async with httpx.AsyncClient() as client:
        response = await client.post(
            f"{BASE_URL}/api/memories/add",
            json={
                "content": "",  # Empty content
                "user_id": "test_user"
            }
        )
        assert response.status_code == 422  # Validation error
