import mimetypes
import os
from asyncio import TimeoutError
from json import loads
from typing import Any, List, Union
from unittest.mock import AsyncMock, MagicMock
from uuid import uuid4

import boto3
import pytest
from agent.tools.sb_deploy_tool import SandboxDeployTool, SandboxDeployToolConfig
from agentpress.tool import <PERSON><PERSON><PERSON><PERSON><PERSON>
from moto import mock_aws
from ninja_common.context import context
from ninja_common.models.context_models import NinjaContextKey
from pydantic import BaseModel


# region Models
class MockFile(BaseModel):
    name: str
    content: str
    is_dir: bool = False


class MockDirectory(BaseModel):
    name: str
    files: List[Union[MockFile, "MockDirectory"]] = []
    is_dir: bool = True

    def get_file(self, name: str):
        for f in self.files:
            if f.name == name:
                return f
        raise FileNotFoundError(name)


MockDirectory.model_rebuild()  # for recursive definition


class MockFilesystem:
    def __init__(self, files=[]):
        self.base_path = "workspace"  # TODO: replace with global constant value
        self.root = MockDirectory(name=self.base_path, files=files)

    # Daytona mock functions
    async def get_file_info(self, path: str):
        try:
            return self._find(path)
        except Exception:
            raise FileNotFoundError(path)

    async def list_files(self, path: str):
        node = self._find(path)
        if not node.is_dir:
            raise NotADirectoryError(path)
        return node.files

    async def download_file(self, path: str):
        node = self._find(path)
        if node.is_dir:
            raise IsADirectoryError(path)
        return node.content

    # Test helper functions
    def _find(self, path: str):
        """Gets a specific file or folder"""
        parts = path.strip("/").split("/")
        if not parts or parts[0] != self.base_path:
            raise FileNotFoundError(path)
        node = self.root
        for part in parts[1:]:
            if not node.is_dir:
                raise NotADirectoryError(path)
            node = node.get_file(part)
        return node


class SandboxDeployToolTestContext(BaseModel):
    """Test context object to pass fixtures around easily"""

    tool: Any = None
    config: Any = None
    deployment_name: Any = None
    deployment_folder: Any = None
    filesystem: Any = None
    s3_client: Any = None
    cf_client: Any = None


# endregion


# region AWS Mocks
@pytest.fixture()
def mock_boto():
    with mock_aws() as mock:
        yield mock


@pytest.fixture()
def mock_s3_client(mock_boto):
    return boto3.client("s3")


@pytest.fixture()
def mock_s3_bucket(mock_s3_client):
    bucket_name = "test-bucket"
    mock_s3_client.create_bucket(
        Bucket=bucket_name, CreateBucketConfiguration={"LocationConstraint": "us-west-2"}
    )
    return bucket_name


@pytest.fixture()
def mock_cf_client(mock_boto):
    return boto3.client("cloudfront")


@pytest.fixture()
def mock_cf_distribution(mock_cf_client):
    response = mock_cf_client.create_distribution(
        DistributionConfig={
            "CallerReference": "my-unique-string",
            "Comment": "",
            "Origins": {
                "Quantity": 1,
                "Items": [
                    {
                        "Id": "origin-1",
                        "DomainName": "example-bucket.s3.amazonaws.com",
                        "S3OriginConfig": {"OriginAccessIdentity": ""},
                    }
                ],
            },
            "DefaultCacheBehavior": {
                "TargetOriginId": "origin-1",
                "ViewerProtocolPolicy": "allow-all",
                "TrustedSigners": {"Enabled": False, "Quantity": 0},
                "ForwardedValues": {"QueryString": False, "Cookies": {"Forward": "none"}},
                "MinTTL": 0,
            },
            "Enabled": True,
        }
    )
    return response["Distribution"]["Id"]


# endregion


# region Test Setup Fixtures
@pytest.fixture(autouse=True)
def set_user_id():
    """Sets the user id for all tests to make sure we can use it in deployment"""
    context[NinjaContextKey.NINJA_USER_ID] = str(uuid4())


@pytest.fixture
def mock_sandbox():
    """Mocks the underlying sandbox functionality"""
    sandbox = MagicMock()
    sandbox.fs = MockFilesystem()
    return sandbox


@pytest.fixture
def tool_config(mock_s3_bucket, mock_cf_distribution):
    """Return the configuration for the tool"""
    return SandboxDeployToolConfig(
        bucket_name=mock_s3_bucket,
        distribution_id=mock_cf_distribution,
        deployed_url="super.test.myninja.ai",
    )


@pytest.fixture
def tool(tool_config, mock_sandbox):
    """Create and return a fully-initialized tool"""
    tool = SandboxDeployTool(
        project_id=str(uuid4()), thread_manager=MagicMock(), config=tool_config
    )
    tool._sandbox = mock_sandbox
    tool._sandbox_id = str(uuid4())
    tool._ensure_sandbox = AsyncMock()
    return tool


# endregion


# region Filesystems
NORMAL_FILESYSTEMS = [
    "mock_filesystem_single_page",
    "mock_filesystem_single_page_with_statics",
    "mock_filesystem_single_page_with_duplicated_nested_statics",
    "mock_filesystem_single_page_with_nested_statics",
    "mock_filesystem_single_page_with_multiple_folders",
    "mock_filesystem_multiple_pages_with_multiple_folders",
]


@pytest.fixture
def mock_filesystem(request):
    """Wrapper to allow for fixtures in parametrize"""
    return request.getfixturevalue(request.param)


@pytest.fixture
def mock_filesystem_single_page():
    """Tests we can deploy a single file"""
    return MockFilesystem(files=[MockFile(name="index.html", content="<html>index</html>")])


@pytest.fixture
def mock_filesystem_single_page_with_statics():
    """Tests we can deploy a page with multiple files"""
    return MockFilesystem(
        files=[
            MockFile(name="index.html", content="<html>index</html>"),
            MockFile(name="style.css", content="body{}"),
            MockFile(name="app.js", content="console.log(1);"),
        ]
    )


@pytest.fixture
def mock_filesystem_single_page_with_nested_statics():
    """Tests we can deploy a page with multiple files in other directories"""
    return MockFilesystem(
        files=[
            MockFile(name="index.html", content="<html>index</html>"),
            MockDirectory(
                name="static",
                files=[
                    MockFile(name="style.css", content="body{}"),
                    MockFile(name="app.js", content="console.log(1);"),
                ],
            ),
        ]
    )


@pytest.fixture
def mock_filesystem_single_page_with_duplicated_nested_statics():
    """Tests we can deploy a page with multiple files of the same name in other directories"""
    return MockFilesystem(
        files=[
            MockFile(name="index.html", content="<html>index</html>"),
            MockFile(name="style.css", content="body{}"),
            MockFile(name="app.js", content="console.log(1);"),
            MockDirectory(
                name="static",
                files=[
                    MockFile(name="style.css", content="body{}"),
                    MockFile(name="app.js", content="console.log(1);"),
                ],
            ),
        ]
    )


@pytest.fixture
def mock_filesystem_single_page_with_multiple_folders():
    """Tests we can deploy a single page with multiple dependent folders"""
    return MockFilesystem(
        files=[
            MockFile(name="index.html", content="<html>index</html>"),
            MockDirectory(
                name="static",
                files=[
                    MockFile(name="style.css", content="body{}"),
                    MockFile(name="app.js", content="console.log(1);"),
                ],
            ),
            MockDirectory(
                name="images",
                files=[
                    MockFile(name="image1", content="<img>image1</img>"),
                    MockFile(name="image2", content="<img>image2</img>"),
                    MockFile(name="image3", content="<img>image3</img>"),
                ],
            ),
        ]
    )


@pytest.fixture
def mock_filesystem_multiple_pages_with_multiple_folders():
    """Tests we can deploy multiple pages with multiple dependent folders"""
    return MockFilesystem(
        files=[
            MockFile(name="index.html", content="<html>index</html>"),
            MockFile(name="page1.html", content="<html>page1</html>"),
            MockFile(name="page2.html", content="<html>page2</html>"),
            MockFile(name="page3.html", content="<html>page3</html>"),
            MockDirectory(
                name="static",
                files=[
                    MockFile(name="style.css", content="body{}"),
                    MockFile(name="app.js", content="console.log(1);"),
                ],
            ),
            MockDirectory(
                name="images",
                files=[
                    MockFile(name="image1", content="<img>image1</img>"),
                    MockFile(name="image2", content="<img>image2</img>"),
                    MockFile(name="image3", content="<img>image3</img>"),
                ],
            ),
        ]
    )


# endregion


# region Helper functions
def assert_success_response(response: ToolResult, ctx: SandboxDeployToolTestContext):
    # Response object and deployment url
    assert response.success == True, "Response is not a success"
    response_data = loads(response.output)
    assert (
        "Website deployed successfully" in response_data["message"]
    ), "Response does not contain success message"
    assert response_data["website-url"].startswith(
        "https://"
    ), "Deployed url does not start with HTTPS header"
    assert (
        ctx.config.deployed_url in response_data["website-url"]
    ), "Deployed url does not contain the deployment domain"
    assert (
        context[NinjaContextKey.NINJA_USER_ID] in response_data["website-url"]
    ), "Deployed url does not contain the user's id"
    assert (
        "index.html" in response_data["website-url"]
    ), "Deployed url does not contain the index page"

    # S3 Uploads
    deployment_hash = ctx.tool._get_deployment_hash(ctx.deployment_name)
    deployment_path = ctx.tool._get_s3_deployment_path(deployment_hash)

    prefix = deployment_path + "/"
    response = ctx.s3_client.list_objects_v2(Bucket=ctx.config.bucket_name, Prefix=prefix)
    assert "Contents" in response, "Malformed s3 list objects query"
    for key in [obj["Key"] for obj in response["Contents"]]:
        filename = key.removeprefix(prefix)
        file = ctx.filesystem._find(os.path.join(ctx.deployment_folder, filename))
        assert not file.is_dir, "Filesystem object retrieved is not a file"
        obj = ctx.s3_client.get_object(Bucket=ctx.config.bucket_name, Key=key)
        body = obj["Body"].read().decode("utf-8")
        content_type = obj["ContentType"]
        expected_type = mimetypes.guess_type(filename)[0] or "text/html"
        assert body == file.content, f"Content mismatch for {filename}"
        assert content_type == expected_type, f"Content type mismatch for {filename}"

    # Cloudfront invalidation
    response = ctx.cf_client.list_invalidations(DistributionId=ctx.config.distribution_id)
    invalidations = response.get("InvalidationList", {}).get("Items", [])
    assert invalidations, "No CloudFront invalidations created"

    # Optionally, check the details of the most recent invalidation
    invalidation_id = invalidations[-1]["Id"]
    invalidation = ctx.cf_client.get_invalidation(
        DistributionId=ctx.config.distribution_id, Id=invalidation_id
    )
    assert invalidation["Invalidation"]["Status"] == "COMPLETED"


def assert_error_response(response: ToolResult, expected_error: str):
    assert response.success == False, "Response is not an error"
    assert (
        "Error deploying website" in response.output
    ), "Response message does not contain indicate error response"
    assert expected_error in response.output, "Expected error was not found in response"


# endregion


# region Positive Cases
@pytest.mark.parametrize(
    "mock_filesystem",
    NORMAL_FILESYSTEMS,
    indirect=True,
)
async def test_deploy_tool(
    tool,
    tool_config,
    mock_filesystem,
    mock_s3_client,
    mock_cf_client,
):
    """Tests that we can successfully deploy from root directory with various different directory structures"""
    # Arrange
    tool.sandbox.fs = mock_filesystem

    # Act
    deployment_name = str(uuid4())
    deployment_folder = tool.sandbox.fs.base_path
    response = await tool.deploy(deployment_name, deployment_folder)

    # Assert
    context = SandboxDeployToolTestContext(
        tool=tool,
        config=tool_config,
        deployment_name=deployment_name,
        deployment_folder=deployment_folder,
        filesystem=tool.sandbox.fs,
        s3_client=mock_s3_client,
        cf_client=mock_cf_client,
    )
    assert_success_response(response, context)


@pytest.mark.parametrize(
    "mock_filesystem",
    NORMAL_FILESYSTEMS,
    indirect=True,
)
async def test_deploy_tool_from_nested_folder(
    tool,
    tool_config,
    mock_filesystem,
    mock_s3_client,
    mock_cf_client,
):
    """Tests that we can successfully deploy from a folder with various different directory structures"""
    # Arrange
    website_folder_name = "website"
    tool.sandbox.fs = MockFilesystem(
        files=[
            # Place another directory in the way of root and files
            MockDirectory(
                name=website_folder_name,
                files=mock_filesystem.root.files,
            )
        ]
    )

    # Act
    deployment_name = str(uuid4())
    deployment_folder = os.path.join(tool.sandbox.fs.base_path, website_folder_name)
    response = await tool.deploy(deployment_name, deployment_folder)

    # Assert
    context = SandboxDeployToolTestContext(
        tool=tool,
        config=tool_config,
        deployment_name=deployment_name,
        deployment_folder=deployment_folder,
        filesystem=tool.sandbox.fs,
        s3_client=mock_s3_client,
        cf_client=mock_cf_client,
    )
    assert_success_response(response, context)


@pytest.mark.parametrize(
    "mock_filesystem",
    NORMAL_FILESYSTEMS,
    indirect=True,
)
async def test_deploy_tool_redeploy(
    tool,
    tool_config,
    mock_filesystem,
    mock_s3_client,
    mock_cf_client,
):
    """Tests that we can successfully deploy various different directory structures, then redeploy them again with the same link"""
    # Arrange
    tool.sandbox.fs = mock_filesystem

    # Act
    deployment_name = str(uuid4())
    deployment_folder = tool.sandbox.fs.base_path
    response_first = await tool.deploy(deployment_name, deployment_folder)

    # Assert
    context = SandboxDeployToolTestContext(
        tool=tool,
        config=tool_config,
        deployment_name=deployment_name,
        deployment_folder=deployment_folder,
        filesystem=tool.sandbox.fs,
        s3_client=mock_s3_client,
        cf_client=mock_cf_client,
    )
    assert_success_response(response_first, context)

    # Act
    response_second = await tool.deploy(deployment_name, deployment_folder)

    # Assert
    assert_success_response(response_second, context)
    # As we are redeploying the website we should see the same deployment url
    deployment_url_first = loads(response_first.output)["website-url"]
    deployment_url_second = loads(response_second.output)["website-url"]
    assert (
        deployment_url_first == deployment_url_second
    ), "Website urls do not match after a redeployment"


@pytest.mark.parametrize(
    "mock_filesystem",
    NORMAL_FILESYSTEMS,
    indirect=True,
)
async def test_deploy_tool_successive_deployments(
    tool,
    tool_config,
    mock_filesystem,
    mock_s3_client,
    mock_cf_client,
):
    """Tests that we can successfully deploy various different directory structures, then redeploy them again with the same link"""
    # Arrange
    tool.sandbox.fs = mock_filesystem

    # Act
    deployment_name = str(uuid4())
    deployment_folder = tool.sandbox.fs.base_path
    response_first = await tool.deploy(deployment_name, deployment_folder)

    # Assert
    context = SandboxDeployToolTestContext(
        tool=tool,
        config=tool_config,
        deployment_name=deployment_name,
        deployment_folder=deployment_folder,
        filesystem=tool.sandbox.fs,
        s3_client=mock_s3_client,
        cf_client=mock_cf_client,
    )
    assert_success_response(response_first, context)

    # Act
    # Generate a new deployment name to simulate asking for a new website
    deployment_name = str(uuid4())
    response_second = await tool.deploy(deployment_name, deployment_folder)

    # Assert
    context.deployment_name = deployment_name
    assert_success_response(response_second, context)
    # As we are redeploying the website we should see the same deployment url
    deployment_url_first = loads(response_first.output)["website-url"]
    deployment_url_second = loads(response_second.output)["website-url"]
    assert (
        deployment_url_first != deployment_url_second
    ), "Website urls match after two successive deployments"


# endregion


# region Negative Cases
async def test_deploy_tool_upload_non_directory(tool, mock_filesystem_single_page):
    """Tests that we get an error when we attempt to upload a file that is not a directory"""
    # Arrange
    tool.sandbox.fs = mock_filesystem_single_page

    # Act
    response = await tool.deploy(str(uuid4()), "index.html")

    # Assert
    assert_error_response(response, f"'index.html' is not a directory")


async def test_deploy_tool_upload_non_existent_directory(tool, mock_filesystem_single_page):
    """Tests that we get an error when we attempt to upload a directory that does not exist"""
    # Arrange
    tool.sandbox.fs = mock_filesystem_single_page

    # Act
    response = await tool.deploy(str(uuid4()), "non_existent_directory")

    # Assert
    assert_error_response(response, "Error occurred checking file info")


async def test_deploy_tool_handles_sandbox_startup_error_gracefully(
    tool, mock_filesystem_single_page
):
    """Tests that we get an error when we attempt to startup sandbox and returns error response"""
    # Arrange
    tool.sandbox.fs = mock_filesystem_single_page
    # Make get_file_info raise an exception
    tool._ensure_sandbox.side_effect = Exception("Failed to get sandbox")

    # Act
    response = await tool.deploy(str(uuid4()), tool.sandbox.fs.base_path)

    # Assert
    assert_error_response(response, "Failed to get sandbox")


async def test_deploy_tool_handles_filesystem_get_file_info_exception_gracefully(
    tool, mock_filesystem_single_page
):
    """Tests that we gracefully handle an error when getting info from the filesystem and returns error response"""
    # Arrange
    tool.sandbox.fs = mock_filesystem_single_page
    # Make get_file_info raise an exception
    tool.sandbox.fs.get_file_info = MagicMock()
    tool.sandbox.fs.get_file_info.side_effect = Exception("Failed to get file info")

    # Act
    response = await tool.deploy(str(uuid4()), tool.sandbox.fs.base_path)

    # Assert
    assert_error_response(response, "Error occurred checking file info")


async def test_deploy_tool_handles_s3_put_object_exception_gracefully(
    tool, mock_filesystem_single_page, mocker
):
    """Tests that we gracefully handle an error when S3 fails to upload and returns error response"""
    # Arrange
    tool.sandbox.fs = mock_filesystem_single_page
    mock_client = MagicMock()
    mock_client.put_object.side_effect = Exception("S3 upload failed")
    mocker.patch("agent.tools.sb_deploy_tool.get_s3_client", return_value=mock_client)

    # Act
    response = await tool.deploy(str(uuid4()), tool.sandbox.fs.base_path)

    # Assert
    assert_error_response(response, "Failed to upload website resources")


async def test_deploy_tool_handles_create_invalidation_exception_gracefully(
    tool, mock_filesystem_single_page, mocker
):
    """Tests that we gracefully handle an error when creating an invalidation and returns error response"""
    # Arrange
    tool.sandbox.fs = mock_filesystem_single_page
    mock_client = MagicMock()
    mock_client.create_invalidation.side_effect = Exception("CloudFront create invalidation error")
    mocker.patch("agent.tools.sb_deploy_tool.get_cloudfront_client", return_value=mock_client)

    # Act
    response = await tool.deploy(str(uuid4()), tool.sandbox.fs.base_path)

    # Assert
    assert_error_response(response, "Failed to refresh website resources")


async def test_deploy_tool_handles_get_invalidation_exception_gracefully(
    tool, mock_filesystem_single_page, mocker
):
    """Tests that we gracefully handle an error when retrieving an invalidation and returns error response"""
    # Arrange
    tool.sandbox.fs = mock_filesystem_single_page
    mock_client = MagicMock()
    mock_client.create_invalidation.return_value = ""  # just need to return, value is irrelevant
    mock_client.get_invalidation.side_effect = Exception("CloudFront get invalidation error")
    mocker.patch("agent.tools.sb_deploy_tool.get_cloudfront_client", return_value=mock_client)

    # Act
    response = await tool.deploy(str(uuid4()), tool.sandbox.fs.base_path)

    # Assert
    assert_error_response(response, "Failed to refresh website resources")


async def test_deploy_tool_handles_invalidation_timeout_exception_gracefully(
    tool, mock_filesystem_single_page, mocker
):
    """Tests that we gracefully handle an error when Cloudfront invalidation times out and returns error response"""
    # Arrange
    tool.sandbox.fs = mock_filesystem_single_page

    # Timeout when we would poll and wait for invalidation to complete
    # function used to avoid runtime error that coro not awaited
    def raise_timeout_and_close(coro, *args, **kwargs):
        # Close the coroutine to avoid warning
        if hasattr(coro, "close"):
            coro.close()
        raise TimeoutError

    mocker.patch("asyncio.wait_for", side_effect=raise_timeout_and_close)

    # Act
    response = await tool.deploy(str(uuid4()), tool.sandbox.fs.base_path)

    # Assert
    assert_error_response(response, "Refreshing website resources timed out")


# endregion
